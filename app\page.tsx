"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Users, 
  FolderOpen, 
  Globe, 
  MessageSquare, 
  TrendingUp, 
  Activity,
  Clock,
  Server,
  BarChart3,
  Zap
} from 'lucide-react'
import { formatNumber, formatDate } from '@/lib/utils'

interface SystemStats {
  totalUsers: number
  totalProjects: number
  deployedProjects: number
  communityProjects: number
  totalMessages: number
}

interface ActivityLog {
  type: string
  target_name: string
  user_name: string
  timestamp: string
}

export default function Dashboard() {
  const router = useRouter()
  const [stats, setStats] = useState<SystemStats | null>(null)
  const [activities, setActivities] = useState<ActivityLog[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [statsRes, activitiesRes] = await Promise.all([
          fetch('/api/admin/stats'),
          fetch('/api/admin/activities?limit=10')
        ])
        
        if (statsRes.ok) {
          const statsData = await statsRes.json()
          setStats(statsData)
        }
        
        if (activitiesRes.ok) {
          const activitiesData = await activitiesRes.json()
          setActivities(activitiesData)
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex-1 p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-64 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="admin-card p-6">
                <div className="h-4 bg-muted rounded w-20 mb-4"></div>
                <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                <div className="h-3 bg-muted rounded w-24"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const statCards = [
    {
      title: '总用户数',
      value: stats?.totalUsers || 0,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      href: '/users',
      description: '注册用户总数'
    },
    {
      title: '总项目数',
      value: stats?.totalProjects || 0,
      icon: FolderOpen,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      href: '/projects',
      description: '创建项目总数'
    },
    {
      title: '已部署项目',
      value: stats?.deployedProjects || 0,
      icon: Server,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      href: '/projects?filter=deployed',
      description: '成功部署项目数'
    },
    {
      title: '社区项目',
      value: stats?.communityProjects || 0,
      icon: Globe,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      href: '/community',
      description: '社区分享项目数'
    },
    {
      title: 'AI对话数',
      value: stats?.totalMessages || 0,
      icon: MessageSquare,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100 dark:bg-pink-900/20',
      href: '/models',
      description: 'AI对话总数'
    }
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registered':
        return <Users className="h-4 w-4 text-blue-600" />
      case 'project_created':
        return <FolderOpen className="h-4 w-4 text-green-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getActivityText = (activity: ActivityLog) => {
    switch (activity.type) {
      case 'user_registered':
        return `用户 ${activity.user_name} 注册了账号`
      case 'project_created':
        return `${activity.user_name} 创建了项目 "${activity.target_name}"`
      default:
        return `${activity.user_name} 执行了 ${activity.type} 操作`
    }
  }

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">LoomRun 管理仪表盘</h1>
        <p className="text-muted-foreground mt-2">
          系统概览和实时数据监控
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        {statCards.map((card) => (
          <div 
            key={card.title} 
            className="admin-stats-card cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105"
            onClick={() => router.push(card.href)}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-2 rounded-lg ${card.bgColor}`}>
                <card.icon className={`h-5 w-5 ${card.color}`} />
              </div>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(card.value)}</p>
              <p className="text-sm text-muted-foreground">{card.title}</p>
              <p className="text-xs text-muted-foreground mt-1">{card.description}</p>
            </div>
          </div>
        ))}
      </div>

      {/* 最近活动 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              最近活动
            </h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {activities.length > 0 ? (
                activities.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="mt-1">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-foreground">
                        {getActivityText(activity)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-muted-foreground text-center py-8">
                  暂无活动记录
                </p>
              )}
            </div>
          </div>
        </div>

        {/* 快速操作 */}
        <div className="admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold">快速操作</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <a
                href="/users"
                className="flex items-center p-3 rounded-lg hover:bg-accent transition-colors"
              >
                <Users className="h-5 w-5 mr-3 text-blue-600" />
                <div>
                  <p className="font-medium">用户管理</p>
                  <p className="text-sm text-muted-foreground">查看和管理用户</p>
                </div>
              </a>
              <a
                href="/projects"
                className="flex items-center p-3 rounded-lg hover:bg-accent transition-colors"
              >
                <FolderOpen className="h-5 w-5 mr-3 text-green-600" />
                <div>
                  <p className="font-medium">项目管理</p>
                  <p className="text-sm text-muted-foreground">管理所有项目</p>
                </div>
              </a>
              <a
                href="/community"
                className="flex items-center p-3 rounded-lg hover:bg-accent transition-colors"
              >
                <Globe className="h-5 w-5 mr-3 text-orange-600" />
                <div>
                  <p className="font-medium">社区管理</p>
                  <p className="text-sm text-muted-foreground">管理社区展示</p>
                </div>
              </a>
              <a
                href="/models"
                className="flex items-center p-3 rounded-lg hover:bg-accent transition-colors"
              >
                <Activity className="h-5 w-5 mr-3 text-purple-600" />
                <div>
                  <p className="font-medium">模型统计</p>
                  <p className="text-sm text-muted-foreground">查看AI模型使用情况</p>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 