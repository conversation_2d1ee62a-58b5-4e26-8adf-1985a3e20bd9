"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/system-config/page",{

/***/ "(app-pages-browser)/./app/system-config/page.tsx":
/*!************************************!*\
  !*** ./app/system-config/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SystemConfigPage() {\n    var _getActivityPointsConfig_invitation, _getActivityPointsConfig_invitation1;\n    _s();\n    const [configs, setConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiModels, setAiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exportTypes, setExportTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [subscriptionPlans, setSubscriptionPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pointsPackages, setPointsPackages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('points');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SystemConfigPage.useEffect\": ()=>{\n            fetchAllData();\n        }\n    }[\"SystemConfigPage.useEffect\"], []);\n    const fetchAllData = async ()=>{\n        try {\n            setLoading(true);\n            const [configRes, modelsRes, exportRes, plansRes, packagesRes] = await Promise.all([\n                fetch('/api/admin/system-config'),\n                fetch('/api/admin/system-config/ai-models'),\n                fetch('/api/admin/system-config/export-types'),\n                fetch('/api/admin/system-config/subscription-plans'),\n                fetch('/api/admin/system-config/points-packages')\n            ]);\n            if (configRes.ok) {\n                const configData = await configRes.json();\n                setConfigs(configData);\n            }\n            if (modelsRes.ok) {\n                const modelsData = await modelsRes.json();\n                setAiModels(modelsData);\n            }\n            if (exportRes.ok) {\n                const exportData = await exportRes.json();\n                setExportTypes(exportData);\n            }\n            if (plansRes.ok) {\n                const plansData = await plansRes.json();\n                setSubscriptionPlans(plansData);\n            }\n            if (packagesRes.ok) {\n                const packagesData = await packagesRes.json();\n                setPointsPackages(packagesData);\n            }\n        } catch (error) {\n            console.error('Failed to fetch system config:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateConfig = (key, value)=>{\n        setConfigs((prev)=>prev.map((config)=>config.config_key === key ? {\n                    ...config,\n                    config_value: value\n                } : config));\n    };\n    const updateAiModel = (id, field, value)=>{\n        setAiModels((prev)=>prev.map((model)=>model.id === id ? {\n                    ...model,\n                    [field]: value\n                } : model));\n    };\n    const updateExportType = (id, field, value)=>{\n        setExportTypes((prev)=>prev.map((type)=>type.id === id ? {\n                    ...type,\n                    [field]: value\n                } : type));\n    };\n    const updateSubscriptionPlan = (id, field, value)=>{\n        setSubscriptionPlans((prev)=>prev.map((plan)=>plan.id === id ? {\n                    ...plan,\n                    [field]: value\n                } : plan));\n    };\n    const updatePointsPackage = (id, field, value)=>{\n        setPointsPackages((prev)=>prev.map((pkg)=>pkg.id === id ? {\n                    ...pkg,\n                    [field]: value\n                } : pkg));\n    };\n    const saveAllChanges = async ()=>{\n        try {\n            setSaving(true);\n            const savePromises = [\n                fetch('/api/admin/system-config', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        configs\n                    })\n                }),\n                fetch('/api/admin/system-config/ai-models', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        models: aiModels\n                    })\n                }),\n                fetch('/api/admin/system-config/export-types', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        types: exportTypes\n                    })\n                }),\n                fetch('/api/admin/system-config/subscription-plans', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        plans: subscriptionPlans\n                    })\n                }),\n                fetch('/api/admin/system-config/points-packages', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        packages: pointsPackages\n                    })\n                })\n            ];\n            const results = await Promise.all(savePromises);\n            const allSuccess = results.every((res)=>res.ok);\n            if (allSuccess) {\n                console.log('所有配置保存成功');\n            // 可以添加成功提示\n            } else {\n                console.error('部分配置保存失败');\n            // 可以添加错误提示\n            }\n        } catch (error) {\n            console.error('Failed to save configs:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const getConfigValue = function(key) {\n        let defaultValue = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '';\n        const config = configs.find((c)=>c.config_key === key);\n        return config ? config.config_value : defaultValue;\n    };\n    // 解析活动积分配置\n    const getActivityPointsConfig = ()=>{\n        try {\n            const configValue = getConfigValue('activity_points_config', '{}');\n            return JSON.parse(configValue);\n        } catch (error) {\n            console.error('Failed to parse activity_points_config:', error);\n            return {\n                invitation: {\n                    validity_days: 15,\n                    description: '邀请活动积分'\n                },\n                registration: {\n                    validity_days: 30,\n                    description: '注册奖励积分'\n                },\n                special_event: {\n                    validity_days: 7,\n                    description: '特殊活动积分'\n                }\n            };\n        }\n    };\n    // 更新活动积分配置\n    const updateActivityPointsConfig = (activityType, validityDays)=>{\n        const currentConfig = getActivityPointsConfig();\n        const updatedConfig = {\n            ...currentConfig,\n            [activityType]: {\n                ...currentConfig[activityType],\n                validity_days: validityDays\n            }\n        };\n        updateConfig('activity_points_config', JSON.stringify(updatedConfig));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-muted rounded w-64 mb-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(10)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 bg-muted rounded\"\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"mr-3 h-8 w-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"系统参数设置\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mt-2\",\n                                    children: \"管理积分系统、AI模型、导出功能、订阅计划等核心参数\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: fetchAllData,\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"刷新\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: saveAllChanges,\n                                    disabled: saving,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        saving ? '保存中...' : '保存所有更改'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                value: activeTab,\n                onValueChange: setActiveTab,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                        className: \"grid w-full grid-cols-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"points\",\n                                children: \"积分设置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"subscription\",\n                                children: \"订阅控制\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"models\",\n                                children: \"AI模型\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"export\",\n                                children: \"导出功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"plans\",\n                                children: \"订阅计划\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"recharge\",\n                                children: \"充值套餐\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"invitation\",\n                                children: \"邀请设置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"points\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"新用户注册设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"启用注册送积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('new_user_points_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('new_user_points_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"注册送积分数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('new_user_points_amount', '100'),\n                                                            onChange: (e)=>updateConfig('new_user_points_amount', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"积分有效期（天）\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('activity_points_validity_days', '30'),\n                                                            onChange: (e)=>updateConfig('activity_points_validity_days', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"积分消费优先级\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-blue-600 text-white text-xs flex items-center justify-center font-bold\",\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"订阅积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"优先消耗订阅获得的积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"活动积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"订阅积分不足时消耗活动积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-purple-600 text-white text-xs flex items-center justify-center font-bold\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"充值积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"最后消耗充值获得的积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"subscription\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"服务总开关\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"订阅服务\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('subscription_service_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('subscription_service_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"充值服务\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('recharge_service_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('recharge_service_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"显示订阅积分按钮\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('show_subscription_button') === '1',\n                                                            onChange: (e)=>updateConfig('show_subscription_button', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-amber-800 dark:text-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"提示：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            '关闭\"显示订阅积分按钮\"可以隐藏用户菜单中的所有充值入口，适合产品前期免费推广阶段。'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"订阅计划控制\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"免费版计划\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('free_plan_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('free_plan_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Pro版计划\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('pro_plan_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('pro_plan_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Max版计划\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('max_plan_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('max_plan_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"独立控制：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"每个计划可以独立开启/关闭，关闭的计划不会在前端显示对应的订阅卡片。\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Max版积分自定义设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"启用Max版积分调整功能\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('max_plan_custom_points_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('max_plan_custom_points_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"最少积分数量\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: getConfigValue('max_plan_min_points', '2500'),\n                                                                    onChange: (e)=>updateConfig('max_plan_min_points', e.target.value),\n                                                                    className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                    min: \"1000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"最多积分数量\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: getConfigValue('max_plan_max_points', '10000'),\n                                                                    onChange: (e)=>updateConfig('max_plan_max_points', e.target.value),\n                                                                    className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                    min: \"2500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"积分调整步长\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: getConfigValue('max_plan_points_step', '500'),\n                                                                    onChange: (e)=>updateConfig('max_plan_points_step', e.target.value),\n                                                                    className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                    min: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-purple-800 dark:text-purple-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Max版特色：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"用户可以在 \",\n                                                            getConfigValue('max_plan_min_points', '2500'),\n                                                            \" - \",\n                                                            getConfigValue('max_plan_max_points', '10000'),\n                                                            \" 积分范围内，按 \",\n                                                            getConfigValue('max_plan_points_step', '500'),\n                                                            \" 积分步长自定义积分数量，系统会根据积分数量动态计算价格。\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"models\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"AI模型积分消耗设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"模型标识\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"模型名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"每次请求积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"排序\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: aiModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-mono\",\n                                                                children: model.model_key\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: model.model_name,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'model_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: model.points_per_request,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'points_per_request', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: model.description || '',\n                                                                    onChange: (e)=>updateAiModel(model.id, 'description', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    placeholder: \"模型描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: model.is_active,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: model.display_order,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'display_order', parseInt(e.target.value) || 999),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, model.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"export\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"导出功能积分消耗设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"导出类型\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"功能名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"积分消耗\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"排序\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: exportTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-mono\",\n                                                                children: type.export_key\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: type.export_name,\n                                                                    onChange: (e)=>updateExportType(type.id, 'export_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: type.points_cost,\n                                                                    onChange: (e)=>updateExportType(type.id, 'points_cost', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: type.description || '',\n                                                                    onChange: (e)=>updateExportType(type.id, 'description', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    placeholder: \"功能描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: type.is_active,\n                                                                    onChange: (e)=>updateExportType(type.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: type.display_order,\n                                                                    onChange: (e)=>updateExportType(type.id, 'display_order', parseInt(e.target.value) || 999),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, type.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"plans\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"订阅计划设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"计划类型\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"计划名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"时长(月)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"原价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"现价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"包含积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"积分有效期(天)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: subscriptionPlans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: plan.plan_type === 'free' ? 'outline' : plan.plan_type === 'pro' ? 'secondary' : 'default',\n                                                                    children: plan.plan_type.toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 716,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: plan.plan_name,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'plan_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: plan.duration_months,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'duration_months', parseInt(e.target.value) || 1),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: plan.original_price,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'original_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: plan.discount_price,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'discount_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: plan.points_included,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'points_included', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: plan.points_validity_days,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'points_validity_days', parseInt(e.target.value) || 30),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: plan.is_active,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, plan.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"recharge\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"充值套餐设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"套餐标识\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"套餐名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"积分数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"原价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"现价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"赠送积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"排序\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: pointsPackages.map((pkg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-mono\",\n                                                                children: pkg.package_key\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: pkg.package_name,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'package_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: pkg.points_amount,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'points_amount', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: pkg.original_price,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'original_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: pkg.discount_price,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'discount_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: pkg.bonus_points,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'bonus_points', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: pkg.description || '',\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'description', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    placeholder: \"套餐描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: pkg.is_active,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 878,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: pkg.display_order,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'display_order', parseInt(e.target.value) || 999),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, pkg.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 795,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"invitation\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"邀请好友设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"启用邀请奖励\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 914,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('invitation_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('invitation_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 915,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"每次邀请奖励积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('invitation_points_per_user', '100'),\n                                                            onChange: (e)=>updateConfig('invitation_points_per_user', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"邀请积分有效期（天）\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: ((_getActivityPointsConfig_invitation = getActivityPointsConfig().invitation) === null || _getActivityPointsConfig_invitation === void 0 ? void 0 : _getActivityPointsConfig_invitation.validity_days) || 15,\n                                                            onChange: (e)=>updateActivityPointsConfig('invitation', parseInt(e.target.value)),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\",\n                                                            max: \"999\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                            children: \"邀请奖励积分的有效期，过期后积分将自动失效\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 942,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"每个用户最多邀请数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('max_invitations_per_user', '10'),\n                                                            onChange: (e)=>updateConfig('max_invitations_per_user', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"计算说明：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"最多可获得邀请积分 = \",\n                                                            getConfigValue('invitation_points_per_user', '100'),\n                                                            \" \\xd7 \",\n                                                            getConfigValue('max_invitations_per_user', '10'),\n                                                            \" = \",\n                                                            parseInt(getConfigValue('invitation_points_per_user', '100')) * parseInt(getConfigValue('max_invitations_per_user', '10')),\n                                                            \" 积分\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 961,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"有效期：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            ((_getActivityPointsConfig_invitation1 = getActivityPointsConfig().invitation) === null || _getActivityPointsConfig_invitation1 === void 0 ? void 0 : _getActivityPointsConfig_invitation1.validity_days) || 15,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 957,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 912,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"邀请码设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 970,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"邀请码长度\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('invitation_code_length', '8'),\n                                                            onChange: (e)=>updateConfig('invitation_code_length', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"4\",\n                                                            max: \"20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 976,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"邀请码过期天数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('invitation_expire_days', '30'),\n                                                            onChange: (e)=>updateConfig('invitation_expire_days', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 989,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                            children: \"邀请码本身的有效期，与积分有效期不同\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 996,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-green-800 dark:text-green-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"邀请机制：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1002,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"每个用户都有唯一的邀请码，成功邀请新用户注册后获得积分奖励。\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1003,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"区别说明：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• 邀请码过期天数：邀请链接的有效期\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1007,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• 邀请积分有效期：获得积分的使用期限\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1000,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 968,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 1018,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"活动积分有效期配置\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1017,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: \"为不同类型的活动积分设置独立的有效期\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1021,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 1016,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: Object.entries(getActivityPointsConfig()).map((param)=>{\n                                                        let [activityType, config] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border border-border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: config.description || activityType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                            lineNumber: 1030,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            children: activityType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                            lineNumber: 1033,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 1029,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: \"有效期（天）\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                            lineNumber: 1038,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: config.validity_days || 30,\n                                                                            onChange: (e)=>updateActivityPointsConfig(activityType, parseInt(e.target.value)),\n                                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                            min: \"1\",\n                                                                            max: \"999\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                            lineNumber: 1039,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 1037,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, activityType, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 1028,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1026,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-amber-800 dark:text-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"配置说明：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"邀请活动积分：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1055,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"通过邀请好友注册获得的积分\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"注册奖励积分：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"新用户注册时获得的积分\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1058,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"特殊活动积分：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1059,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"特殊活动或促销获得的积分\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1060,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"注意：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1061,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"修改有效期只影响新获得的积分，已有积分的有效期不会改变\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1051,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 1015,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 904,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 903,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemConfigPage, \"Nh4uxzK69v1m+lEEzI4/IJ8Yo6M=\");\n_c = SystemConfigPage;\nvar _c;\n$RefreshReg$(_c, \"SystemConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/system-config/page.tsx\n"));

/***/ })

});