/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/users/route";
exports.ids = ["app/api/admin/users/route"];
exports.modules = {

/***/ "(rsc)/./app/api/admin/users/route.ts":
/*!**************************************!*\
  !*** ./app/api/admin/users/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const options = {\n            limit: parseInt(searchParams.get('limit') || '50'),\n            offset: parseInt(searchParams.get('offset') || '0'),\n            search: searchParams.get('search') || '',\n            status: searchParams.get('status') || 'all',\n            membership: searchParams.get('membership') || 'all',\n            activity: searchParams.get('activity') || 'all',\n            sortBy: searchParams.get('sortBy') || 'created_at',\n            sortOrder: searchParams.get('sortOrder') || 'desc'\n        };\n        const users = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.adminQueries.getUsers(options);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            users,\n            total: users.length\n        });\n    } catch (error) {\n        console.error('Failed to fetch users:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/users/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminQueries: () => (/* binding */ adminQueries),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   getPool: () => (/* binding */ getPool)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"(rsc)/./node_modules/mysql2/promise.js\");\n/* harmony import */ var _performance_monitor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./performance-monitor */ \"(rsc)/./lib/performance-monitor.ts\");\n\n\nconst DB_CONFIG = {\n    host: process.env.DB_HOST || 'localhost',\n    port: parseInt(process.env.DB_PORT || '3306'),\n    user: process.env.DB_USER || 'root',\n    password: process.env.DB_PASSWORD || '',\n    database: process.env.DB_NAME || 'loomrun',\n    charset: 'utf8mb4',\n    timezone: '+08:00'\n};\nlet pool = null;\nconst getPool = ()=>{\n    if (!pool) {\n        pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool({\n            ...DB_CONFIG,\n            waitForConnections: true,\n            connectionLimit: 10,\n            queueLimit: 0,\n            idleTimeout: 300000,\n            maxIdle: 10\n        });\n    }\n    return pool;\n};\nconst executeQuery = async (query, params = [])=>{\n    const connection = getPool();\n    let retries = 3;\n    const monitor = _performance_monitor__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getInstance();\n    const startTime = Date.now();\n    while(retries > 0){\n        try {\n            const [results] = await connection.execute(query, params);\n            // 记录查询性能\n            const duration = Date.now() - startTime;\n            monitor.logQuery(query, duration, params);\n            return results;\n        } catch (error) {\n            console.error('Database query error:', error);\n            retries--;\n            if (retries === 0) {\n                // 记录失败查询\n                const duration = Date.now() - startTime;\n                monitor.logQuery(`FAILED: ${query}`, duration, params);\n                throw error;\n            }\n            console.log(`Connection error, retrying... (${retries} attempts left)`);\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n    }\n};\n// Admin specific database queries\nconst adminQueries = {\n    // 获取系统统计信息\n    getSystemStats: async ()=>{\n        // 使用单个查询获取所有统计数据，提高性能\n        const [stats] = await executeQuery(`\n      SELECT\n        (SELECT COUNT(*) FROM users) as total_users,\n        (SELECT COUNT(*) FROM projects) as total_projects,\n        (SELECT COUNT(*) FROM projects WHERE is_deployed = 1) as deployed_projects,\n        (SELECT COUNT(*) FROM community_projects) as community_projects,\n        (SELECT COUNT(*) FROM chat_history) as total_messages,\n        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()) as today_users,\n        (SELECT COUNT(*) FROM projects WHERE DATE(created_at) = CURDATE()) as today_projects,\n        (SELECT COUNT(*) FROM users WHERE last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as active_users\n    `);\n        return {\n            totalUsers: stats.total_users,\n            totalProjects: stats.total_projects,\n            deployedProjects: stats.deployed_projects,\n            communityProjects: stats.community_projects,\n            totalMessages: stats.total_messages,\n            todayUsers: stats.today_users,\n            todayProjects: stats.today_projects,\n            activeUsers: stats.active_users\n        };\n    },\n    // 获取用户列表（优化版）\n    getUsers: async (options = {})=>{\n        const { limit = 50, offset = 0, search = '', status = 'all', membership = 'all', activity = 'all', sortBy = 'created_at', sortOrder = 'desc' } = options;\n        let whereConditions = [];\n        let params = [];\n        // 搜索条件\n        if (search) {\n            whereConditions.push(`(\n        u.nickname LIKE ? OR\n        u.phone LIKE ? OR\n        u.id = ?\n      )`);\n            params.push(`%${search}%`, `%${search}%`, search);\n        }\n        // 状态过滤\n        if (status !== 'all') {\n            whereConditions.push('u.is_active = ?');\n            params.push(status === 'active' ? 1 : 0);\n        }\n        // 活跃度过滤\n        if (activity === 'recent') {\n            whereConditions.push('u.last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)');\n        } else if (activity === 'inactive') {\n            whereConditions.push('(u.last_login IS NULL OR u.last_login < DATE_SUB(NOW(), INTERVAL 30 DAY))');\n        }\n        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';\n        // 排序\n        const validSortFields = [\n            'created_at',\n            'last_login',\n            'points'\n        ];\n        const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';\n        const order = sortOrder === 'asc' ? 'ASC' : 'DESC';\n        // 优化查询：使用预计算的统计数据\n        const users = await executeQuery(`\n      SELECT\n        u.id,\n        u.phone,\n        u.nickname,\n        u.avatar_url,\n        u.created_at,\n        u.updated_at,\n        u.last_login,\n        u.is_active,\n        u.points,\n        u.total_earned_points,\n        u.total_spent_points,\n        COALESCE(stats.project_count, 0) as project_count,\n        COALESCE(stats.message_count, 0) as message_count,\n        COALESCE(stats.community_project_count, 0) as community_project_count,\n        COALESCE(membership.membership_type, 'free') as membership_type,\n        membership.expires_at as membership_expires_at,\n        COALESCE(orders.total_spent, 0) as total_spent\n      FROM users u\n      LEFT JOIN (\n        SELECT\n          u2.id as user_id,\n          COUNT(DISTINCT p.id) as project_count,\n          COUNT(DISTINCT ch.id) as message_count,\n          COUNT(DISTINCT cp.id) as community_project_count\n        FROM users u2\n        LEFT JOIN projects p ON u2.id = p.user_id\n        LEFT JOIN chat_history ch ON u2.id = ch.user_id\n        LEFT JOIN community_projects cp ON u2.id = cp.user_id\n        GROUP BY u2.id\n      ) stats ON u.id = stats.user_id\n      LEFT JOIN (\n        SELECT\n          user_id,\n          membership_type,\n          expires_at\n        FROM membership_orders mo\n        WHERE mo.status = 'paid'\n        AND mo.expires_at > NOW()\n        AND mo.id = (\n          SELECT MAX(id) FROM membership_orders mo2\n          WHERE mo2.user_id = mo.user_id\n          AND mo2.status = 'paid'\n          AND mo2.expires_at > NOW()\n        )\n      ) membership ON u.id = membership.user_id\n      LEFT JOIN (\n        SELECT\n          user_id,\n          SUM(discount_price) as total_spent\n        FROM membership_orders\n        WHERE status = 'paid'\n        GROUP BY user_id\n      ) orders ON u.id = orders.user_id\n      ${whereClause}\n      ORDER BY u.${sortField} ${order}\n      LIMIT ? OFFSET ?\n    `, [\n            ...params,\n            limit,\n            offset\n        ]);\n        return users;\n    },\n    // 获取项目列表\n    getProjects: async (limit = 50, offset = 0)=>{\n        const projects = await executeQuery(`\n      SELECT \n        p.id,\n        p.title,\n        p.user_id,\n        p.is_deployed,\n        p.deploy_url,\n        p.created_at,\n        p.updated_at,\n        u.nickname as user_name,\n        u.phone as user_phone,\n        COUNT(DISTINCT pv.id) as version_count,\n        COUNT(DISTINCT ch.id) as message_count\n      FROM projects p\n      LEFT JOIN users u ON p.user_id = u.id\n      LEFT JOIN project_versions pv ON p.id = pv.project_id\n      LEFT JOIN chat_history ch ON p.id = ch.project_id\n      GROUP BY p.id\n      ORDER BY p.updated_at DESC\n      LIMIT ${limit} OFFSET ${offset}\n    `, []);\n        return projects;\n    },\n    // 获取社区项目列表\n    getCommunityProjects: async (limit = 50, offset = 0)=>{\n        // 首先尝试使用display_order字段\n        try {\n            const projects = await executeQuery(`\n        SELECT \n          cp.id,\n          cp.title,\n          cp.user_id,\n          cp.original_project_id,\n          cp.html_content,\n          cp.created_at,\n          cp.updated_at,\n          u.nickname as user_name,\n          u.phone as user_phone,\n          p.title as original_title,\n          COALESCE(cp.display_order, 999999) as display_order\n        FROM community_projects cp\n        LEFT JOIN users u ON cp.user_id = u.id\n        LEFT JOIN projects p ON cp.original_project_id = p.id\n        ORDER BY cp.display_order ASC, cp.created_at DESC\n        LIMIT ${limit} OFFSET ${offset}\n      `, []);\n            return projects;\n        } catch (error) {\n            // 如果display_order字段不存在，使用ROW_NUMBER作为备用\n            if (error.code === 'ER_BAD_FIELD_ERROR') {\n                console.log('display_order字段不存在，使用备用查询');\n                const projects = await executeQuery(`\n          SELECT \n            cp.id,\n            cp.title,\n            cp.user_id,\n            cp.original_project_id,\n            cp.html_content,\n            cp.created_at,\n            cp.updated_at,\n            u.nickname as user_name,\n            u.phone as user_phone,\n            p.title as original_title,\n            ROW_NUMBER() OVER (ORDER BY cp.created_at DESC) as display_order\n          FROM community_projects cp\n          LEFT JOIN users u ON cp.user_id = u.id\n          LEFT JOIN projects p ON cp.original_project_id = p.id\n          ORDER BY cp.created_at DESC\n          LIMIT ${limit} OFFSET ${offset}\n        `, []);\n                return projects;\n            }\n            throw error;\n        }\n    },\n    // 获取模型使用统计\n    getModelStats: async (period = '30d')=>{\n        let dateCondition = '';\n        switch(period){\n            case '1d':\n                dateCondition = 'WHERE date >= CURDATE()';\n                break;\n            case '7d':\n                dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';\n                break;\n            case '30d':\n                dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';\n                break;\n            case '1y':\n                dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)';\n                break;\n            default:\n                dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';\n        }\n        // 基于 usage_stats 表的真实统计\n        const stats = await executeQuery(`\n      SELECT \n        'deepseek-chat' as model,\n        COALESCE(SUM(requests_count), 0) as requests,\n        COALESCE(SUM(requests_count * 100), 0) as tokens,\n        COALESCE(SUM(cost_yuan), 0) as cost\n      FROM usage_stats\n      ${dateCondition}\n      UNION ALL\n      SELECT \n        'doubao-seed-1-6-250615' as model,\n        COUNT(DISTINCT ch.id) as requests,\n        COUNT(DISTINCT ch.id) * 80 as tokens,\n        COUNT(DISTINCT ch.id) * 0.0024 as cost\n      FROM chat_history ch\n      ${dateCondition.replace('date >=', 'DATE(ch.created_at) >=')}\n        AND ch.message_type = 'ai'\n    `, []);\n        return stats;\n    },\n    // 获取用户Token使用统计\n    getUserTokenStats: async (period = '30d')=>{\n        let dateCondition = '';\n        switch(period){\n            case '1d':\n                dateCondition = 'AND us.date >= CURDATE()';\n                break;\n            case '7d':\n                dateCondition = 'AND us.date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';\n                break;\n            case '30d':\n                dateCondition = 'AND us.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';\n                break;\n            case '1y':\n                dateCondition = 'AND us.date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)';\n                break;\n            default:\n                dateCondition = 'AND us.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';\n        }\n        const stats = await executeQuery(`\n      SELECT \n        u.id as user_id,\n        u.nickname,\n        u.phone,\n        COALESCE(SUM(us.requests_count), 0) as total_requests,\n        COALESCE(SUM(us.requests_count * 100), 0) as total_tokens,\n        COALESCE(SUM(us.cost_yuan), 0) as total_cost,\n        COALESCE(MAX(us.membership_type), 'free') as membership_type,\n        COUNT(DISTINCT us.date) as active_days\n      FROM users u\n      LEFT JOIN usage_stats us ON u.id = us.user_id ${dateCondition}\n      GROUP BY u.id, u.nickname, u.phone\n      HAVING total_requests > 0\n      ORDER BY total_tokens DESC\n      LIMIT 50\n    `, []);\n        return stats;\n    },\n    // 获取Token使用趋势\n    getTokenTrends: async (period = '30d')=>{\n        let dateCondition = '';\n        let groupBy = '';\n        switch(period){\n            case '7d':\n                dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';\n                groupBy = 'DATE(date)';\n                break;\n            case '30d':\n                dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';\n                groupBy = 'DATE(date)';\n                break;\n            case '1y':\n                dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)';\n                groupBy = 'DATE_FORMAT(date, \"%Y-%m\")';\n                break;\n            default:\n                dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';\n                groupBy = 'DATE(date)';\n        }\n        const trends = await executeQuery(`\n      SELECT \n        ${groupBy} as period,\n        SUM(requests_count) as requests,\n        SUM(requests_count * 100) as tokens,\n        SUM(cost_yuan) as cost,\n        COUNT(DISTINCT user_id) as active_users\n      FROM usage_stats\n      ${dateCondition}\n      GROUP BY ${groupBy}\n      ORDER BY period ASC\n    `, []);\n        return trends;\n    },\n    // 获取用户统计信息\n    getUserStats: async ()=>{\n        const [totalUsers] = await executeQuery('SELECT COUNT(*) as count FROM users');\n        const [activeUsers] = await executeQuery('SELECT COUNT(*) as count FROM users WHERE is_active = 1');\n        const [newUsersToday] = await executeQuery('SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()');\n        // 获取会员用户数\n        const [proUsers] = await executeQuery(`\n      SELECT COUNT(DISTINCT mo.user_id) as count\n      FROM membership_orders mo\n      WHERE mo.membership_type = 'pro'\n      AND mo.status = 'paid'\n      AND mo.expires_at > NOW()\n    `);\n        const [maxUsers] = await executeQuery(`\n      SELECT COUNT(DISTINCT mo.user_id) as count\n      FROM membership_orders mo\n      WHERE mo.membership_type = 'max'\n      AND mo.status = 'paid'\n      AND mo.expires_at > NOW()\n    `);\n        // 获取总收入\n        const [totalRevenue] = await executeQuery(`\n      SELECT COALESCE(SUM(discount_price), 0) as total\n      FROM membership_orders\n      WHERE status = 'paid'\n    `);\n        return {\n            totalUsers: totalUsers.count,\n            activeUsers: activeUsers.count,\n            newUsersToday: newUsersToday.count,\n            proUsers: proUsers.count,\n            maxUsers: maxUsers.count,\n            totalRevenue: totalRevenue.total\n        };\n    },\n    // 获取用户详情（增强版）\n    getUserDetail: async (userId)=>{\n        const [user] = await executeQuery(`\n      SELECT\n        u.*,\n        COUNT(DISTINCT p.id) as project_count,\n        COUNT(DISTINCT ch.id) as message_count,\n        COUNT(DISTINCT cp.id) as community_project_count,\n        (\n          SELECT mo.membership_type\n          FROM membership_orders mo\n          WHERE mo.user_id = u.id\n          AND mo.status = 'paid'\n          AND mo.expires_at > NOW()\n          ORDER BY mo.expires_at DESC\n          LIMIT 1\n        ) as membership_type,\n        (\n          SELECT mo.expires_at\n          FROM membership_orders mo\n          WHERE mo.user_id = u.id\n          AND mo.status = 'paid'\n          AND mo.expires_at > NOW()\n          ORDER BY mo.expires_at DESC\n          LIMIT 1\n        ) as membership_expires_at,\n        (\n          SELECT SUM(mo.discount_price)\n          FROM membership_orders mo\n          WHERE mo.user_id = u.id\n          AND mo.status = 'paid'\n        ) as total_spent\n      FROM users u\n      LEFT JOIN projects p ON u.id = p.user_id\n      LEFT JOIN chat_history ch ON u.id = ch.user_id\n      LEFT JOIN community_projects cp ON u.id = cp.user_id\n      WHERE u.id = ?\n      GROUP BY u.id\n    `, [\n            userId\n        ]);\n        if (!user) return null;\n        // 获取用户的使用统计\n        const [usageStats] = await executeQuery(`\n      SELECT\n        SUM(requests_count) as total_requests,\n        SUM(requests_count * 100) as total_tokens,\n        SUM(cost_yuan) as total_cost,\n        COUNT(DISTINCT date) as active_days\n      FROM usage_stats\n      WHERE user_id = ?\n      AND date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\n    `, [\n            userId\n        ]);\n        // 获取用户的项目列表\n        const projects = await executeQuery(`\n      SELECT\n        p.id,\n        p.title,\n        p.is_deployed,\n        p.created_at,\n        p.updated_at,\n        COUNT(DISTINCT pv.id) as version_count,\n        COUNT(DISTINCT ch.id) as message_count\n      FROM projects p\n      LEFT JOIN project_versions pv ON p.id = pv.project_id\n      LEFT JOIN chat_history ch ON p.id = ch.project_id\n      WHERE p.user_id = ?\n      GROUP BY p.id\n      ORDER BY p.updated_at DESC\n      LIMIT 10\n    `, [\n            userId\n        ]);\n        // 获取用户的会员订单\n        const orders = await executeQuery(`\n      SELECT\n        id,\n        order_no,\n        membership_type,\n        duration_months,\n        discount_price,\n        status,\n        paid_at,\n        expires_at,\n        created_at\n      FROM membership_orders\n      WHERE user_id = ?\n      ORDER BY created_at DESC\n      LIMIT 5\n    `, [\n            userId\n        ]);\n        return {\n            ...user,\n            usage_stats: {\n                total_requests: usageStats?.total_requests || 0,\n                total_tokens: usageStats?.total_tokens || 0,\n                total_cost: usageStats?.total_cost || 0,\n                active_days: usageStats?.active_days || 0\n            },\n            projects,\n            orders\n        };\n    },\n    // 更新用户状态\n    updateUserStatus: async (userId, isActive)=>{\n        await executeQuery(`\n      UPDATE users\n      SET is_active = ?, updated_at = NOW()\n      WHERE id = ?\n    `, [\n            isActive ? 1 : 0,\n            userId\n        ]);\n    },\n    // 导出用户数据\n    exportUsers: async ()=>{\n        const users = await executeQuery(`\n      SELECT\n        u.id,\n        u.nickname,\n        u.phone,\n        u.created_at,\n        u.last_login,\n        u.is_active,\n        COUNT(DISTINCT p.id) as project_count,\n        COUNT(DISTINCT ch.id) as message_count,\n        (\n          SELECT mo.membership_type\n          FROM membership_orders mo\n          WHERE mo.user_id = u.id\n          AND mo.status = 'paid'\n          AND mo.expires_at > NOW()\n          ORDER BY mo.expires_at DESC\n          LIMIT 1\n        ) as membership_type,\n        (\n          SELECT SUM(mo.discount_price)\n          FROM membership_orders mo\n          WHERE mo.user_id = u.id\n          AND mo.status = 'paid'\n        ) as total_spent\n      FROM users u\n      LEFT JOIN projects p ON u.id = p.user_id\n      LEFT JOIN chat_history ch ON u.id = ch.user_id\n      GROUP BY u.id\n      ORDER BY u.created_at DESC\n    `);\n        return users;\n    },\n    // 更新社区项目顺序\n    updateCommunityProjectOrder: async (projectId, newOrder)=>{\n        try {\n            await executeQuery(`\n        UPDATE community_projects \n        SET display_order = ? \n        WHERE id = ?\n      `, [\n                newOrder,\n                projectId\n            ]);\n        } catch (error) {\n            // 如果display_order字段不存在，抛出友好的错误信息\n            if (error.code === 'ER_BAD_FIELD_ERROR' && error.sqlMessage?.includes('display_order')) {\n                throw new Error('display_order字段不存在，请先运行数据库迁移添加该字段');\n            }\n            throw error;\n        }\n    },\n    // 获取系统活动日志\n    getActivityLogs: async (limit = 100, offset = 0)=>{\n        // 这里可以根据实际的日志表来查询\n        // 目前先返回基于现有数据的活动记录\n        const activities = await executeQuery(`\n      SELECT \n        'project_created' as type,\n        p.id as target_id,\n        p.title as target_name,\n        p.user_id,\n        u.nickname as user_name,\n        p.created_at as timestamp\n      FROM projects p\n      LEFT JOIN users u ON p.user_id = u.id\n      UNION ALL\n      SELECT \n        'user_registered' as type,\n        u.id as target_id,\n        u.nickname as target_name,\n        u.id as user_id,\n        u.nickname as user_name,\n        u.created_at as timestamp\n      FROM users u\n      ORDER BY timestamp DESC\n      LIMIT ${limit} OFFSET ${offset}\n    `, []);\n        return activities;\n    },\n    // ==================== 积分管理相关查询 ====================\n    // 获取积分统计信息（优化版）\n    getPointsStats: async ()=>{\n        // 使用单个查询获取所有积分统计数据\n        const [stats] = await executeQuery(`\n      SELECT\n        (SELECT COALESCE(SUM(points), 0) FROM users) as total_points,\n        (SELECT COALESCE(SUM(total_earned_points), 0) FROM users) as total_earned,\n        (SELECT COALESCE(SUM(total_spent_points), 0) FROM users) as total_spent,\n        (SELECT COALESCE(SUM(points_expired), 0) FROM points_expiry_log) as total_expired,\n        (SELECT COUNT(*) FROM users WHERE points > 0) as active_users,\n        (SELECT COUNT(*) FROM points_transactions) as total_transactions,\n        (SELECT COALESCE(AVG(points), 0) FROM users WHERE points > 0) as avg_balance,\n        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()) as new_users_today\n    `);\n        return {\n            totalPoints: stats.total_points,\n            totalEarned: stats.total_earned,\n            totalSpent: stats.total_spent,\n            totalExpired: stats.total_expired,\n            activeUsers: stats.active_users,\n            totalTransactions: stats.total_transactions,\n            averageBalance: Math.round(stats.avg_balance),\n            newUsersToday: stats.new_users_today\n        };\n    },\n    // 获取用户积分信息列表（修复版）\n    getUserPointsList: async (limit = 50, offset = 0)=>{\n        const users = await executeQuery(`\n      SELECT\n        u.id,\n        u.nickname,\n        u.phone,\n        u.points,\n        u.total_earned_points,\n        u.total_spent_points,\n        u.created_at,\n        pt_last.last_transaction_at,\n        COALESCE(pb.activity_points, 0) as activity_points,\n        COALESCE(pb.subscription_points, 0) as subscription_points,\n        COALESCE(pb.recharge_points, 0) as recharge_points\n      FROM users u\n      LEFT JOIN (\n        SELECT\n          user_id,\n          MAX(created_at) as last_transaction_at\n        FROM points_transactions\n        GROUP BY user_id\n      ) pt_last ON u.id = pt_last.user_id\n      LEFT JOIN (\n        SELECT\n          user_id,\n          SUM(CASE WHEN points_type = 'activity' AND is_active = 1 THEN points_amount ELSE 0 END) as activity_points,\n          SUM(CASE WHEN points_type = 'subscription' AND is_active = 1 THEN points_amount ELSE 0 END) as subscription_points,\n          SUM(CASE WHEN points_type = 'recharge' AND is_active = 1 THEN points_amount ELSE 0 END) as recharge_points\n        FROM user_points_balance\n        GROUP BY user_id\n      ) pb ON u.id = pb.user_id\n      ORDER BY u.points DESC\n      LIMIT ${limit} OFFSET ${offset}\n    `);\n        return users.map((user)=>({\n                ...user,\n                points_breakdown: {\n                    activity: user.activity_points || 0,\n                    subscription: user.subscription_points || 0,\n                    recharge: user.recharge_points || 0\n                }\n            }));\n    },\n    // 获取积分交易记录（优化版）\n    getPointsTransactions: async (options = {})=>{\n        const { limit = 50, offset = 0, transactionType = 'all', sourceType = 'all', pointsType = 'all', dateRange = '30d', search = '', sortBy = 'created_at', sortOrder = 'desc' } = options;\n        let whereConditions = [\n            '1=1'\n        ]; // 基础条件\n        let params = [];\n        // 时间范围过滤（优先处理，利用索引）\n        if (dateRange !== 'all') {\n            const days = parseInt(dateRange.replace('d', ''));\n            whereConditions.push('pt.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)');\n            params.push(days);\n        }\n        // 交易类型过滤\n        if (transactionType !== 'all') {\n            whereConditions.push('pt.transaction_type = ?');\n            params.push(transactionType);\n        }\n        // 来源类型过滤\n        if (sourceType !== 'all') {\n            whereConditions.push('pt.source_type = ?');\n            params.push(sourceType);\n        }\n        // 积分类型过滤\n        if (pointsType !== 'all') {\n            whereConditions.push('pt.points_type = ?');\n            params.push(pointsType);\n        }\n        // 搜索条件（最后处理）\n        if (search) {\n            whereConditions.push(`(\n        u.nickname LIKE ? OR\n        pt.id = ? OR\n        pt.description LIKE ?\n      )`);\n            params.push(`%${search}%`, search, `%${search}%`);\n        }\n        const whereClause = `WHERE ${whereConditions.join(' AND ')}`;\n        // 排序\n        const validSortFields = [\n            'created_at',\n            'points_amount',\n            'balance_after'\n        ];\n        const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';\n        const order = sortOrder === 'asc' ? 'ASC' : 'DESC';\n        const transactions = await executeQuery(`\n      SELECT\n        pt.id,\n        pt.user_id,\n        COALESCE(u.nickname, CONCAT('用户', pt.user_id)) as user_name,\n        pt.transaction_type,\n        pt.points_amount,\n        pt.balance_before,\n        pt.balance_after,\n        pt.source_type,\n        pt.points_type,\n        pt.description,\n        pt.created_at\n      FROM points_transactions pt\n      LEFT JOIN users u ON pt.user_id = u.id\n      ${whereClause}\n      ORDER BY pt.${sortField} ${order}\n      LIMIT ? OFFSET ?\n    `, [\n            ...params,\n            limit,\n            offset\n        ]);\n        return transactions;\n    },\n    // 获取用户积分详情（优化版）\n    getUserPointsDetail: async (userId)=>{\n        const [user] = await executeQuery(`\n      SELECT\n        u.id,\n        u.nickname,\n        u.phone,\n        u.points,\n        u.total_earned_points,\n        u.total_spent_points,\n        u.created_at,\n        pb.activity_points,\n        pb.subscription_points,\n        pb.recharge_points\n      FROM users u\n      LEFT JOIN (\n        SELECT\n          user_id,\n          SUM(CASE WHEN points_type = 'activity' AND is_active = 1 THEN points_amount ELSE 0 END) as activity_points,\n          SUM(CASE WHEN points_type = 'subscription' AND is_active = 1 THEN points_amount ELSE 0 END) as subscription_points,\n          SUM(CASE WHEN points_type = 'recharge' AND is_active = 1 THEN points_amount ELSE 0 END) as recharge_points\n        FROM user_points_balance\n        WHERE user_id = ${userId}\n        GROUP BY user_id\n      ) pb ON u.id = pb.user_id\n      WHERE u.id = ${userId}\n    `);\n        if (!user) return null;\n        // 获取最近的交易记录\n        const recentTransactions = await executeQuery(`\n      SELECT\n        id,\n        transaction_type,\n        points_amount,\n        balance_before,\n        balance_after,\n        source_type,\n        points_type,\n        description,\n        created_at\n      FROM points_transactions\n      WHERE user_id = ${userId}\n      ORDER BY created_at DESC\n      LIMIT 10\n    `);\n        // 获取积分余额详情（包含过期时间）\n        const pointsBalance = await executeQuery(`\n      SELECT\n        points_type,\n        points_amount,\n        expires_at,\n        is_active,\n        created_at\n      FROM user_points_balance\n      WHERE user_id = ${userId} AND is_active = 1 AND points_amount > 0\n      ORDER BY expires_at ASC, created_at ASC\n    `);\n        return {\n            ...user,\n            points_breakdown: {\n                activity: user.activity_points || 0,\n                subscription: user.subscription_points || 0,\n                recharge: user.recharge_points || 0\n            },\n            recent_transactions: recentTransactions,\n            points_balance_details: pointsBalance\n        };\n    },\n    // 调整用户积分（精准版本）\n    adjustUserPoints: async (userId, amount, type, pointsType = 'activity', validityDays = null, reason = '管理员调整')=>{\n        const connection = getPool();\n        const conn = await connection.getConnection();\n        try {\n            await conn.beginTransaction();\n            // 获取当前积分余额\n            const [currentUser] = await conn.execute(`\n        SELECT points, total_earned_points, total_spent_points\n        FROM users\n        WHERE id = ?\n      `, [\n                userId\n            ]);\n            if (!currentUser || currentUser.length === 0) {\n                throw new Error('用户不存在');\n            }\n            const currentBalance = currentUser[0].points;\n            const currentEarned = currentUser[0].total_earned_points;\n            const currentSpent = currentUser[0].total_spent_points;\n            // 计算新余额\n            const newBalance = type === 'add' ? currentBalance + amount : currentBalance - amount;\n            if (newBalance < 0) {\n                throw new Error('积分余额不足');\n            }\n            // 更新用户积分\n            const newEarned = type === 'add' ? currentEarned + amount : currentEarned;\n            const newSpent = type === 'subtract' ? currentSpent + amount : currentSpent;\n            await conn.execute(`\n        UPDATE users\n        SET points = ?,\n            total_earned_points = ?,\n            total_spent_points = ?,\n            updated_at = NOW()\n        WHERE id = ?\n      `, [\n                newBalance,\n                newEarned,\n                newSpent,\n                userId\n            ]);\n            // 如果是增加积分，需要在积分余额表中记录\n            if (type === 'add') {\n                const expiresAt = validityDays && validityDays < 999999 ? new Date(Date.now() + validityDays * 24 * 60 * 60 * 1000) : null;\n                await conn.execute(`\n          INSERT INTO user_points_balance (\n            user_id,\n            points_type,\n            points_amount,\n            expires_at,\n            is_active,\n            created_at\n          ) VALUES (?, ?, ?, ?, 1, NOW())\n        `, [\n                    userId,\n                    pointsType,\n                    amount,\n                    expiresAt\n                ]);\n            } else {\n                // 如果是扣减积分，需要从积分余额表中扣减（按消耗顺序：订阅->活动->充值）\n                let remainingAmount = amount;\n                // 按积分类型优先级扣减：subscription -> activity -> recharge\n                const priorityOrder = [\n                    'subscription',\n                    'activity',\n                    'recharge'\n                ];\n                for (const currentType of priorityOrder){\n                    if (remainingAmount <= 0) break;\n                    // 获取该类型的积分余额记录（按过期时间排序，先过期的先扣）\n                    const [balanceRecords] = await conn.execute(`\n            SELECT id, points_amount, expires_at\n            FROM user_points_balance\n            WHERE user_id = ? AND points_type = ? AND is_active = 1 AND points_amount > 0\n            ORDER BY expires_at ASC, created_at ASC\n          `, [\n                        userId,\n                        currentType\n                    ]);\n                    for (const record of balanceRecords){\n                        if (remainingAmount <= 0) break;\n                        const deductAmount = Math.min(remainingAmount, record.points_amount);\n                        const newRecordAmount = record.points_amount - deductAmount;\n                        // 记录消耗日志\n                        await conn.execute(`\n              INSERT INTO points_consumption_log (\n                user_id,\n                transaction_id,\n                balance_record_id,\n                points_consumed,\n                created_at\n              ) VALUES (?, LAST_INSERT_ID(), ?, ?, NOW())\n            `, [\n                            userId,\n                            record.id,\n                            deductAmount\n                        ]);\n                        if (newRecordAmount <= 0) {\n                            // 完全扣完，标记为非活跃\n                            await conn.execute(`\n                UPDATE user_points_balance\n                SET points_amount = 0, is_active = 0, updated_at = NOW()\n                WHERE id = ?\n              `, [\n                                record.id\n                            ]);\n                        } else {\n                            // 部分扣减\n                            await conn.execute(`\n                UPDATE user_points_balance\n                SET points_amount = ?, updated_at = NOW()\n                WHERE id = ?\n              `, [\n                                newRecordAmount,\n                                record.id\n                            ]);\n                        }\n                        remainingAmount -= deductAmount;\n                    }\n                }\n            }\n            // 记录交易\n            await conn.execute(`\n        INSERT INTO points_transactions (\n          user_id,\n          transaction_type,\n          points_amount,\n          balance_before,\n          balance_after,\n          source_type,\n          points_type,\n          description,\n          created_at\n        ) VALUES (?, ?, ?, ?, ?, 'admin_adjust', ?, ?, NOW())\n      `, [\n                userId,\n                type === 'add' ? 'earn' : 'spend',\n                amount,\n                currentBalance,\n                newBalance,\n                type === 'add' ? pointsType : null,\n                reason\n            ]);\n            await conn.commit();\n            return {\n                success: true,\n                newBalance,\n                pointsType: type === 'add' ? pointsType : null,\n                validityDays: type === 'add' ? validityDays : null,\n                message: `积分${type === 'add' ? '增加' : '扣减'}成功`\n            };\n        } catch (error) {\n            await conn.rollback();\n            throw error;\n        } finally{\n            conn.release();\n        }\n    },\n    // 导出积分数据\n    exportPointsData: async (type)=>{\n        if (type === 'users') {\n            return await executeQuery(`\n        SELECT\n          u.id,\n          u.nickname,\n          u.phone,\n          u.points,\n          u.total_earned_points,\n          u.total_spent_points,\n          u.created_at,\n          (\n            SELECT MAX(pt.created_at)\n            FROM points_transactions pt\n            WHERE pt.user_id = u.id\n          ) as last_transaction_at\n        FROM users u\n        ORDER BY u.points DESC\n      `);\n        } else {\n            return await executeQuery(`\n        SELECT\n          pt.id,\n          pt.user_id,\n          u.nickname as user_name,\n          pt.transaction_type,\n          pt.points_amount,\n          pt.balance_before,\n          pt.balance_after,\n          pt.source_type,\n          pt.points_type,\n          pt.description,\n          pt.created_at\n        FROM points_transactions pt\n        LEFT JOIN users u ON pt.user_id = u.id\n        ORDER BY pt.created_at DESC\n      `);\n        }\n    },\n    // 处理过期积分\n    processExpiredPoints: async ()=>{\n        const connection = getPool();\n        const conn = await connection.getConnection();\n        try {\n            await conn.beginTransaction();\n            // 查找所有过期的积分记录\n            const [expiredRecords] = await conn.execute(`\n        SELECT\n          upb.id,\n          upb.user_id,\n          upb.points_type,\n          upb.points_amount,\n          upb.expires_at,\n          u.points as current_points\n        FROM user_points_balance upb\n        JOIN users u ON upb.user_id = u.id\n        WHERE upb.is_active = 1\n        AND upb.expires_at IS NOT NULL\n        AND upb.expires_at <= NOW()\n        AND upb.points_amount > 0\n      `);\n            let totalExpiredPoints = 0;\n            const expiredUsers = new Map();\n            for (const record of expiredRecords){\n                // 标记积分记录为过期\n                await conn.execute(`\n          UPDATE user_points_balance\n          SET is_active = 0, updated_at = NOW()\n          WHERE id = ?\n        `, [\n                    record.id\n                ]);\n                // 累计用户过期积分\n                if (!expiredUsers.has(record.user_id)) {\n                    expiredUsers.set(record.user_id, {\n                        userId: record.user_id,\n                        currentPoints: record.current_points,\n                        expiredAmount: 0\n                    });\n                }\n                const userExpired = expiredUsers.get(record.user_id);\n                userExpired.expiredAmount += record.points_amount;\n                totalExpiredPoints += record.points_amount;\n                // 记录过期日志\n                await conn.execute(`\n          INSERT INTO points_expiry_log (\n            user_id,\n            points_type,\n            points_expired,\n            expired_at,\n            created_at\n          ) VALUES (?, ?, ?, ?, NOW())\n        `, [\n                    record.user_id,\n                    record.points_type,\n                    record.points_amount,\n                    record.expires_at\n                ]);\n            }\n            // 更新用户积分余额\n            for (const [userId, userData] of expiredUsers){\n                const newBalance = Math.max(0, userData.currentPoints - userData.expiredAmount);\n                await conn.execute(`\n          UPDATE users\n          SET points = ?, updated_at = NOW()\n          WHERE id = ?\n        `, [\n                    newBalance,\n                    userId\n                ]);\n                // 记录积分过期交易\n                await conn.execute(`\n          INSERT INTO points_transactions (\n            user_id,\n            transaction_type,\n            points_amount,\n            balance_before,\n            balance_after,\n            source_type,\n            description,\n            created_at\n          ) VALUES (?, 'spend', ?, ?, ?, 'system_expire', '积分过期', NOW())\n        `, [\n                    userId,\n                    userData.expiredAmount,\n                    userData.currentPoints,\n                    newBalance\n                ]);\n            }\n            await conn.commit();\n            return {\n                success: true,\n                expiredRecordsCount: expiredRecords.length,\n                totalExpiredPoints,\n                affectedUsersCount: expiredUsers.size\n            };\n        } catch (error) {\n            await conn.rollback();\n            throw error;\n        } finally{\n            conn.release();\n        }\n    },\n    // 获取即将过期的积分\n    getExpiringPoints: async (days = 7)=>{\n        const expiringPoints = await executeQuery(`\n      SELECT\n        upb.id,\n        upb.user_id,\n        u.nickname,\n        u.phone,\n        upb.points_type,\n        upb.points_amount,\n        upb.expires_at,\n        DATEDIFF(upb.expires_at, NOW()) as days_until_expiry\n      FROM user_points_balance upb\n      JOIN users u ON upb.user_id = u.id\n      WHERE upb.is_active = 1\n      AND upb.expires_at IS NOT NULL\n      AND upb.expires_at > NOW()\n      AND upb.expires_at <= DATE_ADD(NOW(), INTERVAL ? DAY)\n      AND upb.points_amount > 0\n      ORDER BY upb.expires_at ASC\n    `, [\n            days\n        ]);\n        return expiringPoints;\n    },\n    // ==================== 系统配置管理相关查询 ====================\n    // 获取系统配置\n    getSystemConfigs: async ()=>{\n        const configs = await executeQuery(`\n      SELECT\n        id,\n        setting_key as config_key,\n        setting_value as config_value,\n        setting_type as config_type,\n        description,\n        category,\n        is_active\n      FROM system_settings\n      WHERE is_active = 1\n      ORDER BY category, setting_key\n    `);\n        return configs;\n    },\n    // 更新系统配置\n    updateSystemConfigs: async (configs)=>{\n        const connection = getPool();\n        const conn = await connection.getConnection();\n        try {\n            await conn.beginTransaction();\n            for (const config of configs){\n                await conn.execute(`\n          UPDATE system_settings\n          SET setting_value = ?, updated_at = NOW()\n          WHERE setting_key = ?\n        `, [\n                    config.config_value,\n                    config.config_key\n                ]);\n            }\n            await conn.commit();\n        } catch (error) {\n            await conn.rollback();\n            throw error;\n        } finally{\n            conn.release();\n        }\n    },\n    // 获取AI模型配置\n    getAiModels: async ()=>{\n        const models = await executeQuery(`\n      SELECT\n        id,\n        model_key,\n        model_name,\n        points_per_request,\n        description,\n        is_active,\n        display_order\n      FROM ai_models\n      ORDER BY display_order, id\n    `);\n        return models;\n    },\n    // 更新AI模型配置\n    updateAiModels: async (models)=>{\n        const connection = getPool();\n        const conn = await connection.getConnection();\n        try {\n            await conn.beginTransaction();\n            for (const model of models){\n                await conn.execute(`\n          UPDATE ai_models\n          SET\n            model_name = ?,\n            points_per_request = ?,\n            description = ?,\n            is_active = ?,\n            display_order = ?,\n            updated_at = NOW()\n          WHERE id = ?\n        `, [\n                    model.model_name,\n                    model.points_per_request,\n                    model.description,\n                    model.is_active ? 1 : 0,\n                    model.display_order,\n                    model.id\n                ]);\n            }\n            await conn.commit();\n        } catch (error) {\n            await conn.rollback();\n            throw error;\n        } finally{\n            conn.release();\n        }\n    },\n    // 获取导出类型配置\n    getExportTypes: async ()=>{\n        const types = await executeQuery(`\n      SELECT\n        id,\n        export_key,\n        export_name,\n        points_cost,\n        description,\n        is_active,\n        display_order\n      FROM export_types\n      ORDER BY display_order, id\n    `);\n        return types;\n    },\n    // 更新导出类型配置\n    updateExportTypes: async (types)=>{\n        const connection = getPool();\n        const conn = await connection.getConnection();\n        try {\n            await conn.beginTransaction();\n            for (const type of types){\n                await conn.execute(`\n          UPDATE export_types\n          SET\n            export_name = ?,\n            points_cost = ?,\n            description = ?,\n            is_active = ?,\n            display_order = ?,\n            updated_at = NOW()\n          WHERE id = ?\n        `, [\n                    type.export_name,\n                    type.points_cost,\n                    type.description,\n                    type.is_active ? 1 : 0,\n                    type.display_order,\n                    type.id\n                ]);\n            }\n            await conn.commit();\n        } catch (error) {\n            await conn.rollback();\n            throw error;\n        } finally{\n            conn.release();\n        }\n    },\n    // 获取订阅计划配置\n    getSubscriptionPlans: async ()=>{\n        const plans = await executeQuery(`\n      SELECT\n        id,\n        plan_key,\n        plan_type,\n        plan_name,\n        duration_months,\n        original_price,\n        discount_price,\n        points_included,\n        points_validity_days,\n        features,\n        is_active,\n        display_order\n      FROM subscription_plans\n      ORDER BY display_order, id\n    `);\n        return plans;\n    },\n    // 更新订阅计划配置\n    updateSubscriptionPlans: async (plans)=>{\n        const connection = getPool();\n        const conn = await connection.getConnection();\n        try {\n            await conn.beginTransaction();\n            for (const plan of plans){\n                await conn.execute(`\n          UPDATE subscription_plans\n          SET\n            plan_name = ?,\n            duration_months = ?,\n            original_price = ?,\n            discount_price = ?,\n            points_included = ?,\n            points_validity_days = ?,\n            features = ?,\n            is_active = ?,\n            display_order = ?,\n            updated_at = NOW()\n          WHERE id = ?\n        `, [\n                    plan.plan_name,\n                    plan.duration_months,\n                    plan.original_price,\n                    plan.discount_price,\n                    plan.points_included,\n                    plan.points_validity_days,\n                    JSON.stringify(plan.features),\n                    plan.is_active ? 1 : 0,\n                    plan.display_order,\n                    plan.id\n                ]);\n            }\n            await conn.commit();\n        } catch (error) {\n            await conn.rollback();\n            throw error;\n        } finally{\n            conn.release();\n        }\n    },\n    // 获取充值套餐配置\n    getPointsPackages: async ()=>{\n        const packages = await executeQuery(`\n      SELECT\n        id,\n        package_key,\n        package_name,\n        points_amount,\n        original_price,\n        discount_price,\n        bonus_points,\n        description,\n        is_active,\n        display_order\n      FROM points_packages\n      ORDER BY display_order, id\n    `);\n        return packages;\n    },\n    // 更新充值套餐配置\n    updatePointsPackages: async (packages)=>{\n        const connection = getPool();\n        const conn = await connection.getConnection();\n        try {\n            await conn.beginTransaction();\n            for (const pkg of packages){\n                await conn.execute(`\n          UPDATE points_packages\n          SET\n            package_name = ?,\n            points_amount = ?,\n            original_price = ?,\n            discount_price = ?,\n            bonus_points = ?,\n            description = ?,\n            is_active = ?,\n            display_order = ?,\n            updated_at = NOW()\n          WHERE id = ?\n        `, [\n                    pkg.package_name,\n                    pkg.points_amount,\n                    pkg.original_price,\n                    pkg.discount_price,\n                    pkg.bonus_points,\n                    pkg.description,\n                    pkg.is_active ? 1 : 0,\n                    pkg.display_order,\n                    pkg.id\n                ]);\n            }\n            await conn.commit();\n        } catch (error) {\n            await conn.rollback();\n            throw error;\n        } finally{\n            conn.release();\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./lib/performance-monitor.ts":
/*!************************************!*\
  !*** ./lib/performance-monitor.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   measureQueryPerformance: () => (/* binding */ measureQueryPerformance)\n/* harmony export */ });\n// 性能监控工具\nclass PerformanceMonitor {\n    static getInstance() {\n        if (!PerformanceMonitor.instance) {\n            PerformanceMonitor.instance = new PerformanceMonitor();\n        }\n        return PerformanceMonitor.instance;\n    }\n    // 记录查询性能\n    logQuery(query, duration, params) {\n        const log = {\n            query: query.replace(/\\s+/g, ' ').trim(),\n            duration,\n            timestamp: new Date(),\n            params\n        };\n        this.queryLogs.push(log);\n        // 只保留最近1000条记录\n        if (this.queryLogs.length > 1000) {\n            this.queryLogs = this.queryLogs.slice(-1000);\n        }\n        // 记录慢查询\n        if (duration > this.slowQueryThreshold) {\n            console.warn(`🐌 慢查询检测 (${duration}ms):`, {\n                query: log.query,\n                params: log.params,\n                duration: `${duration}ms`\n            });\n        }\n    }\n    // 获取性能统计\n    getPerformanceStats() {\n        if (this.queryLogs.length === 0) {\n            return {\n                totalQueries: 0,\n                averageDuration: 0,\n                slowQueries: 0,\n                fastestQuery: 0,\n                slowestQuery: 0\n            };\n        }\n        const durations = this.queryLogs.map((log)=>log.duration);\n        const slowQueries = this.queryLogs.filter((log)=>log.duration > this.slowQueryThreshold);\n        return {\n            totalQueries: this.queryLogs.length,\n            averageDuration: Math.round(durations.reduce((a, b)=>a + b, 0) / durations.length),\n            slowQueries: slowQueries.length,\n            fastestQuery: Math.min(...durations),\n            slowestQuery: Math.max(...durations),\n            slowQueryThreshold: this.slowQueryThreshold\n        };\n    }\n    // 获取慢查询列表\n    getSlowQueries(limit = 10) {\n        return this.queryLogs.filter((log)=>log.duration > this.slowQueryThreshold).sort((a, b)=>b.duration - a.duration).slice(0, limit);\n    }\n    // 获取查询频率统计\n    getQueryFrequency() {\n        const frequency = {};\n        this.queryLogs.forEach((log)=>{\n            // 提取查询类型（SELECT, INSERT, UPDATE, DELETE）\n            const queryType = log.query.split(' ')[0].toUpperCase();\n            frequency[queryType] = (frequency[queryType] || 0) + 1;\n        });\n        return frequency;\n    }\n    // 清除日志\n    clearLogs() {\n        this.queryLogs = [];\n    }\n    // 设置慢查询阈值\n    setSlowQueryThreshold(threshold) {\n        this.slowQueryThreshold = threshold;\n    }\n    constructor(){\n        this.queryLogs = [];\n        this.slowQueryThreshold = 1000 // 1秒\n        ;\n    }\n}\n// 查询性能装饰器\nfunction measureQueryPerformance(target, propertyName, descriptor) {\n    const method = descriptor.value;\n    const monitor = PerformanceMonitor.getInstance();\n    descriptor.value = async function(...args) {\n        const startTime = Date.now();\n        try {\n            const result = await method.apply(this, args);\n            const duration = Date.now() - startTime;\n            // 记录性能\n            monitor.logQuery(propertyName, duration, args);\n            return result;\n        } catch (error) {\n            const duration = Date.now() - startTime;\n            monitor.logQuery(`${propertyName} (ERROR)`, duration, args);\n            throw error;\n        }\n    };\n    return descriptor;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PerformanceMonitor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/performance-monitor.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$":
/*!****************************************************!*\
  !*** ./node_modules/mysql2/lib/ sync ^cardinal.*$ ***!
  \****************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.ts&appDir=C%3A%5CUsers%5CAbduwali%5CDesktop%5C333%5CLoomRun_admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbduwali%5CDesktop%5C333%5CLoomRun_admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.ts&appDir=C%3A%5CUsers%5CAbduwali%5CDesktop%5C333%5CLoomRun_admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbduwali%5CDesktop%5C333%5CLoomRun_admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Abduwali_Desktop_333_LoomRun_admin_app_api_admin_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/users/route.ts */ \"(rsc)/./app/api/admin/users/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/users/route\",\n        pathname: \"/api/admin/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/users/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\api\\\\admin\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Abduwali_Desktop_333_LoomRun_admin_app_api_admin_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.ts&appDir=C%3A%5CUsers%5CAbduwali%5CDesktop%5C333%5CLoomRun_admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbduwali%5CDesktop%5C333%5CLoomRun_admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "timers":
/*!*************************!*\
  !*** external "timers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("timers");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mysql2","vendor-chunks/aws-ssl-profiles","vendor-chunks/iconv-lite","vendor-chunks/long","vendor-chunks/lru-cache","vendor-chunks/denque","vendor-chunks/is-property","vendor-chunks/lru.min","vendor-chunks/sqlstring","vendor-chunks/seq-queue","vendor-chunks/named-placeholders","vendor-chunks/generate-function","vendor-chunks/safer-buffer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fusers%2Froute&page=%2Fapi%2Fadmin%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fusers%2Froute.ts&appDir=C%3A%5CUsers%5CAbduwali%5CDesktop%5C333%5CLoomRun_admin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAbduwali%5CDesktop%5C333%5CLoomRun_admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();