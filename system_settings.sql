/*
 Navicat Premium Dump SQL

 Source Server         : root
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : localhost:3306
 Source Schema         : loomrun

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 31/07/2025 07:05:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for system_settings
-- ----------------------------
DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `setting_type` enum('string','number','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'general',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_setting_key`(`setting_key` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  INDEX `idx_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of system_settings
-- ----------------------------
INSERT INTO `system_settings` VALUES (1, 'new_user_points_enabled', '0', 'boolean', '新用户注册送积分功能开关', 'points', 1, '2025-07-30 03:24:25', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (2, 'new_user_points_amount', '50', 'number', '新用户注册送积分数量', 'points', 1, '2025-07-30 03:24:25', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (3, 'recharge_service_enabled', '1', 'boolean', '充值服务功能开关', 'payment', 1, '2025-07-30 03:24:25', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (4, 'subscription_service_enabled', '1', 'boolean', '订阅服务功能开关', 'payment', 1, '2025-07-30 03:24:25', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (5, 'invitation_enabled', '1', 'boolean', '邀请功能开关', 'invitation', 1, '2025-07-30 03:30:19', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (6, 'invitation_points_per_user', '100', 'number', '每邀请一个用户获得的积分', 'invitation', 1, '2025-07-30 03:30:19', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (7, 'max_invitations_per_user', '10', 'number', '每个用户最多可邀请的用户数量', 'invitation', 1, '2025-07-30 03:30:19', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (8, 'invitation_code_length', '8', 'number', '邀请码长度', 'invitation', 1, '2025-07-30 03:30:19', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (9, 'invitation_expire_days', '30', 'number', '邀请码过期天数', 'invitation', 1, '2025-07-30 03:30:19', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (10, 'activity_points_validity_days', '15', 'number', '活动积分有效期天数', 'points', 1, '2025-07-30 04:09:16', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (13, 'points_consumption_priority', 'subscription,activity,recharge', 'string', '积分消费优先级顺序', 'points', 1, '2025-07-30 04:09:16', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (24, 'free_plan_enabled', '1', 'boolean', '免费版计划开关', 'subscription', 1, '2025-07-31 02:46:17', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (25, 'pro_plan_enabled', '1', 'boolean', 'Pro版计划开关', 'subscription', 1, '2025-07-31 02:46:17', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (26, 'max_plan_enabled', '1', 'boolean', 'Max版计划开关', 'subscription', 1, '2025-07-31 02:46:17', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (27, 'show_subscription_button', '1', 'boolean', '显示订阅积分按钮', 'ui', 1, '2025-07-31 02:46:17', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (28, 'max_plan_custom_points_enabled', '1', 'boolean', 'Max版支持积分调整功能', 'subscription', 1, '2025-07-31 02:46:17', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (29, 'max_plan_min_points', '2500', 'number', 'Max版最少积分数量', 'subscription', 1, '2025-07-31 02:46:17', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (30, 'max_plan_max_points', '10000', 'number', 'Max版最多积分数量', 'subscription', 1, '2025-07-31 02:46:17', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (31, 'max_plan_points_step', '500', 'number', 'Max版积分调整步长', 'subscription', 1, '2025-07-31 02:46:17', '2025-07-31 06:09:05');
INSERT INTO `system_settings` VALUES (34, 'activity_points_config', '{\"invitation\": {\"validity_days\": 15, \"description\": \"邀请活动积分\"}, \"registration\": {\"validity_days\": 30, \"description\": \"注册奖励积分\"}, \"special_event\": {\"validity_days\": 7, \"description\": \"特殊活动积分\"}}', 'json', '不同活动积分有效期配置', 'points', 1, '2025-07-31 06:59:23', '2025-07-31 06:59:23');
INSERT INTO `system_settings` VALUES (35, 'show_invitation_banner', '1', 'boolean', '显示邀请活动横幅', 'ui', 1, '2025-07-31 06:59:23', '2025-07-31 06:59:23');
INSERT INTO `system_settings` VALUES (36, 'invitation_banner_text', '🎉 邀请好友注册，每成功邀请1人获得积分奖励！', 'string', '邀请横幅显示文字', 'ui', 1, '2025-07-31 06:59:23', '2025-07-31 06:59:23');

SET FOREIGN_KEY_CHECKS = 1;
