import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = parseInt(params.id)
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      )
    }
    
    const body = await request.json()
    const { amount, type, pointsType, validityDays, reason } = body

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Amount must be a positive number' },
        { status: 400 }
      )
    }

    if (!['add', 'subtract'].includes(type)) {
      return NextResponse.json(
        { error: 'Type must be either "add" or "subtract"' },
        { status: 400 }
      )
    }

    // 验证积分类型（仅在增加时需要）
    if (type === 'add' && pointsType && !['activity', 'subscription', 'recharge'].includes(pointsType)) {
      return NextResponse.json(
        { error: 'Invalid points type' },
        { status: 400 }
      )
    }

    // 验证有效期（仅在增加时需要）
    if (type === 'add' && validityDays && (validityDays <= 0 || validityDays > 999999)) {
      return NextResponse.json(
        { error: 'Invalid validity days' },
        { status: 400 }
      )
    }

    const result = await adminQueries.adjustUserPoints(
      userId,
      parseInt(amount),
      type,
      pointsType || 'activity',
      validityDays ? parseInt(validityDays) : null,
      reason || '管理员调整'
    )
    
    return NextResponse.json(result)
  } catch (error: any) {
    console.error('Failed to adjust user points:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}
