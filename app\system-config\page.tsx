"use client"

import { useEffect, useState } from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Coins,
  Users,
  Zap,
  Star,
  Gift,
  Download,
  MessageSquare,
  CreditCard,
  UserPlus,
  Activity
} from 'lucide-react'
import { formatNumber, formatCurrency } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'

interface SystemConfig {
  id: number
  config_key: string
  config_value: string
  config_type: 'string' | 'number' | 'boolean' | 'json'
  description: string
  category: string
  is_active: boolean
}

interface AIModel {
  id: number
  model_key: string
  model_name: string
  points_per_request: number
  description: string
  is_active: boolean
  display_order: number
}

interface ExportType {
  id: number
  export_key: string
  export_name: string
  points_cost: number
  description: string
  is_active: boolean
  display_order: number
}

interface SubscriptionPlan {
  id: number
  plan_key: string
  plan_type: 'free' | 'pro' | 'max'
  plan_name: string
  duration_months: number
  original_price: number
  discount_price: number
  points_included: number
  points_validity_days: number
  features: any
  is_active: boolean
  display_order: number
}

interface PointsPackage {
  id: number
  package_key: string
  package_name: string
  points_amount: number
  original_price: number
  discount_price: number
  bonus_points: number
  description: string
  is_active: boolean
  display_order: number
}

export default function SystemConfigPage() {
  const [configs, setConfigs] = useState<SystemConfig[]>([])
  const [aiModels, setAiModels] = useState<AIModel[]>([])
  const [exportTypes, setExportTypes] = useState<ExportType[]>([])
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([])
  const [pointsPackages, setPointsPackages] = useState<PointsPackage[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('points')

  useEffect(() => {
    fetchAllData()
  }, [])

  const fetchAllData = async () => {
    try {
      setLoading(true)
      const [configRes, modelsRes, exportRes, plansRes, packagesRes] = await Promise.all([
        fetch('/api/admin/system-config'),
        fetch('/api/admin/system-config/ai-models'),
        fetch('/api/admin/system-config/export-types'),
        fetch('/api/admin/system-config/subscription-plans'),
        fetch('/api/admin/system-config/points-packages')
      ])

      if (configRes.ok) {
        const configData = await configRes.json()
        setConfigs(configData)
      }

      if (modelsRes.ok) {
        const modelsData = await modelsRes.json()
        setAiModels(modelsData)
      }

      if (exportRes.ok) {
        const exportData = await exportRes.json()
        setExportTypes(exportData)
      }

      if (plansRes.ok) {
        const plansData = await plansRes.json()
        setSubscriptionPlans(plansData)
      }

      if (packagesRes.ok) {
        const packagesData = await packagesRes.json()
        setPointsPackages(packagesData)
      }
    } catch (error) {
      console.error('Failed to fetch system config:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateConfig = (key: string, value: string) => {
    setConfigs(prev => prev.map(config =>
      config.config_key === key ? { ...config, config_value: value } : config
    ))
  }

  const updateAiModel = (id: number, field: string, value: any) => {
    setAiModels(prev => prev.map(model => 
      model.id === id ? { ...model, [field]: value } : model
    ))
  }

  const updateExportType = (id: number, field: string, value: any) => {
    setExportTypes(prev => prev.map(type => 
      type.id === id ? { ...type, [field]: value } : type
    ))
  }

  const updateSubscriptionPlan = (id: number, field: string, value: any) => {
    setSubscriptionPlans(prev => prev.map(plan => 
      plan.id === id ? { ...plan, [field]: value } : plan
    ))
  }

  const updatePointsPackage = (id: number, field: string, value: any) => {
    setPointsPackages(prev => prev.map(pkg => 
      pkg.id === id ? { ...pkg, [field]: value } : pkg
    ))
  }

  const saveAllChanges = async () => {
    try {
      setSaving(true)
      
      const savePromises = [
        fetch('/api/admin/system-config', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ configs })
        }),
        fetch('/api/admin/system-config/ai-models', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ models: aiModels })
        }),
        fetch('/api/admin/system-config/export-types', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ types: exportTypes })
        }),
        fetch('/api/admin/system-config/subscription-plans', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ plans: subscriptionPlans })
        }),
        fetch('/api/admin/system-config/points-packages', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ packages: pointsPackages })
        })
      ]

      const results = await Promise.all(savePromises)
      const allSuccess = results.every(res => res.ok)

      if (allSuccess) {
        console.log('所有配置保存成功')
        // 可以添加成功提示
      } else {
        console.error('部分配置保存失败')
        // 可以添加错误提示
      }
    } catch (error) {
      console.error('Failed to save configs:', error)
    } finally {
      setSaving(false)
    }
  }

  const getConfigValue = (key: string, defaultValue: string = '') => {
    const config = configs.find(c => c.config_key === key)
    return config ? config.config_value : defaultValue
  }

  // 解析活动积分配置
  const getActivityPointsConfig = () => {
    try {
      const configValue = getConfigValue('activity_points_config', '{}')
      return JSON.parse(configValue)
    } catch (error) {
      console.error('Failed to parse activity_points_config:', error)
      return {
        invitation: { validity_days: 15, description: '邀请活动积分' },
        registration: { validity_days: 30, description: '注册奖励积分' },
        special_event: { validity_days: 7, description: '特殊活动积分' }
      }
    }
  }

  // 更新活动积分配置
  const updateActivityPointsConfig = (activityType: string, validityDays: number) => {
    const currentConfig = getActivityPointsConfig()
    const updatedConfig = {
      ...currentConfig,
      [activityType]: {
        ...currentConfig[activityType],
        validity_days: validityDays
      }
    }
    updateConfig('activity_points_config', JSON.stringify(updatedConfig))
  }

  if (loading) {
    return (
      <div className="flex-1 p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-64 mb-8"></div>
          <div className="space-y-4">
            {[...Array(10)].map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              <Sliders className="mr-3 h-8 w-8" />
              系统参数设置
            </h1>
            <p className="text-muted-foreground mt-2">
              管理积分系统、AI模型、导出功能、订阅计划等核心参数
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={fetchAllData} disabled={loading}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button onClick={saveAllChanges} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? '保存中...' : '保存所有更改'}
            </Button>
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="points">积分设置</TabsTrigger>
          <TabsTrigger value="subscription">订阅控制</TabsTrigger>
          <TabsTrigger value="models">AI模型</TabsTrigger>
          <TabsTrigger value="export">导出功能</TabsTrigger>
          <TabsTrigger value="plans">订阅计划</TabsTrigger>
          <TabsTrigger value="recharge">充值套餐</TabsTrigger>
          <TabsTrigger value="invitation">邀请设置</TabsTrigger>
        </TabsList>

        {/* 积分设置 */}
        <TabsContent value="points" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 新用户注册设置 */}
            <div className="admin-card">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold flex items-center">
                  <UserPlus className="h-5 w-5 mr-2" />
                  新用户注册设置
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">启用注册送积分</label>
                  <input
                    type="checkbox"
                    checked={getConfigValue('new_user_points_enabled') === '1'}
                    onChange={(e) => updateConfig('new_user_points_enabled', e.target.checked ? '1' : '0')}
                    className="rounded"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">注册送积分数量</label>
                  <input
                    type="number"
                    value={getConfigValue('new_user_points_amount', '100')}
                    onChange={(e) => updateConfig('new_user_points_amount', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                    min="0"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">积分有效期（天）</label>
                  <input
                    type="number"
                    value={getConfigValue('activity_points_validity_days', '30')}
                    onChange={(e) => updateConfig('activity_points_validity_days', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                    min="1"
                  />
                </div>
              </div>
            </div>



            {/* 积分消费优先级 */}
            <div className="admin-card">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  积分消费优先级
                </h3>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="w-6 h-6 rounded-full bg-blue-600 text-white text-xs flex items-center justify-center font-bold">1</div>
                    <div>
                      <div className="font-medium">订阅积分</div>
                      <div className="text-sm text-muted-foreground">优先消耗订阅获得的积分</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold">2</div>
                    <div>
                      <div className="font-medium">活动积分</div>
                      <div className="text-sm text-muted-foreground">订阅积分不足时消耗活动积分</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="w-6 h-6 rounded-full bg-purple-600 text-white text-xs flex items-center justify-center font-bold">3</div>
                    <div>
                      <div className="font-medium">充值积分</div>
                      <div className="text-sm text-muted-foreground">最后消耗充值获得的积分</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* 订阅控制 */}
        <TabsContent value="subscription" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 总体服务控制 */}
            <div className="admin-card">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  服务总开关
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">订阅服务</label>
                  <input
                    type="checkbox"
                    checked={getConfigValue('subscription_service_enabled') === '1'}
                    onChange={(e) => updateConfig('subscription_service_enabled', e.target.checked ? '1' : '0')}
                    className="rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">充值服务</label>
                  <input
                    type="checkbox"
                    checked={getConfigValue('recharge_service_enabled') === '1'}
                    onChange={(e) => updateConfig('recharge_service_enabled', e.target.checked ? '1' : '0')}
                    className="rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">显示订阅积分按钮</label>
                  <input
                    type="checkbox"
                    checked={getConfigValue('show_subscription_button') === '1'}
                    onChange={(e) => updateConfig('show_subscription_button', e.target.checked ? '1' : '0')}
                    className="rounded"
                  />
                </div>
                <div className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                  <div className="text-sm text-amber-800 dark:text-amber-200">
                    <strong>提示：</strong>关闭"显示订阅积分按钮"可以隐藏用户菜单中的所有充值入口，适合产品前期免费推广阶段。
                  </div>
                </div>
              </div>
            </div>

            {/* 订阅计划控制 */}
            <div className="admin-card">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold flex items-center">
                  <Star className="h-5 w-5 mr-2" />
                  订阅计划控制
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">免费版计划</label>
                  <input
                    type="checkbox"
                    checked={getConfigValue('free_plan_enabled') === '1'}
                    onChange={(e) => updateConfig('free_plan_enabled', e.target.checked ? '1' : '0')}
                    className="rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Pro版计划</label>
                  <input
                    type="checkbox"
                    checked={getConfigValue('pro_plan_enabled') === '1'}
                    onChange={(e) => updateConfig('pro_plan_enabled', e.target.checked ? '1' : '0')}
                    className="rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Max版计划</label>
                  <input
                    type="checkbox"
                    checked={getConfigValue('max_plan_enabled') === '1'}
                    onChange={(e) => updateConfig('max_plan_enabled', e.target.checked ? '1' : '0')}
                    className="rounded"
                  />
                </div>
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>独立控制：</strong>每个计划可以独立开启/关闭，关闭的计划不会在前端显示对应的订阅卡片。
                  </div>
                </div>
              </div>
            </div>

            {/* Max版积分自定义设置 */}
            <div className="admin-card lg:col-span-2">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold flex items-center">
                  <Zap className="h-5 w-5 mr-2" />
                  Max版积分自定义设置
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">启用Max版积分调整功能</label>
                  <input
                    type="checkbox"
                    checked={getConfigValue('max_plan_custom_points_enabled') === '1'}
                    onChange={(e) => updateConfig('max_plan_custom_points_enabled', e.target.checked ? '1' : '0')}
                    className="rounded"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium">最少积分数量</label>
                    <input
                      type="number"
                      value={getConfigValue('max_plan_min_points', '2500')}
                      onChange={(e) => updateConfig('max_plan_min_points', e.target.value)}
                      className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                      min="1000"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">最多积分数量</label>
                    <input
                      type="number"
                      value={getConfigValue('max_plan_max_points', '10000')}
                      onChange={(e) => updateConfig('max_plan_max_points', e.target.value)}
                      className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                      min="2500"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">积分调整步长</label>
                    <input
                      type="number"
                      value={getConfigValue('max_plan_points_step', '500')}
                      onChange={(e) => updateConfig('max_plan_points_step', e.target.value)}
                      className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                      min="100"
                    />
                  </div>
                </div>
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="text-sm text-purple-800 dark:text-purple-200">
                    <strong>Max版特色：</strong>用户可以在 {getConfigValue('max_plan_min_points', '2500')} - {getConfigValue('max_plan_max_points', '10000')} 积分范围内，按 {getConfigValue('max_plan_points_step', '500')} 积分步长自定义积分数量，系统会根据积分数量动态计算价格。
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* AI模型设置 */}
        <TabsContent value="models" className="space-y-6">
          <div className="admin-card">
            <div className="p-6 border-b border-border">
              <h3 className="text-lg font-semibold flex items-center">
                <MessageSquare className="h-5 w-5 mr-2" />
                AI模型积分消耗设置
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>模型标识</th>
                    <th>模型名称</th>
                    <th>每次请求积分</th>
                    <th>描述</th>
                    <th>状态</th>
                    <th>排序</th>
                  </tr>
                </thead>
                <tbody>
                  {aiModels.map((model) => (
                    <tr key={model.id}>
                      <td className="font-mono">{model.model_key}</td>
                      <td>
                        <input
                          type="text"
                          value={model.model_name}
                          onChange={(e) => updateAiModel(model.id, 'model_name', e.target.value)}
                          className="w-full px-2 py-1 border border-input rounded bg-background text-foreground"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={model.points_per_request}
                          onChange={(e) => updateAiModel(model.id, 'points_per_request', parseInt(e.target.value) || 0)}
                          className="w-20 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          value={model.description || ''}
                          onChange={(e) => updateAiModel(model.id, 'description', e.target.value)}
                          className="w-full px-2 py-1 border border-input rounded bg-background text-foreground"
                          placeholder="模型描述"
                        />
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          checked={model.is_active}
                          onChange={(e) => updateAiModel(model.id, 'is_active', e.target.checked)}
                          className="rounded"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={model.display_order}
                          onChange={(e) => updateAiModel(model.id, 'display_order', parseInt(e.target.value) || 999)}
                          className="w-16 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        {/* 导出功能设置 */}
        <TabsContent value="export" className="space-y-6">
          <div className="admin-card">
            <div className="p-6 border-b border-border">
              <h3 className="text-lg font-semibold flex items-center">
                <Download className="h-5 w-5 mr-2" />
                导出功能积分消耗设置
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>导出类型</th>
                    <th>功能名称</th>
                    <th>积分消耗</th>
                    <th>描述</th>
                    <th>状态</th>
                    <th>排序</th>
                  </tr>
                </thead>
                <tbody>
                  {exportTypes.map((type) => (
                    <tr key={type.id}>
                      <td className="font-mono">{type.export_key}</td>
                      <td>
                        <input
                          type="text"
                          value={type.export_name}
                          onChange={(e) => updateExportType(type.id, 'export_name', e.target.value)}
                          className="w-full px-2 py-1 border border-input rounded bg-background text-foreground"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={type.points_cost}
                          onChange={(e) => updateExportType(type.id, 'points_cost', parseInt(e.target.value) || 0)}
                          className="w-20 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          value={type.description || ''}
                          onChange={(e) => updateExportType(type.id, 'description', e.target.value)}
                          className="w-full px-2 py-1 border border-input rounded bg-background text-foreground"
                          placeholder="功能描述"
                        />
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          checked={type.is_active}
                          onChange={(e) => updateExportType(type.id, 'is_active', e.target.checked)}
                          className="rounded"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={type.display_order}
                          onChange={(e) => updateExportType(type.id, 'display_order', parseInt(e.target.value) || 999)}
                          className="w-16 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        {/* 订阅计划设置 */}
        <TabsContent value="plans" className="space-y-6">
          <div className="admin-card">
            <div className="p-6 border-b border-border">
              <h3 className="text-lg font-semibold flex items-center">
                <Star className="h-5 w-5 mr-2" />
                订阅计划设置
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>计划类型</th>
                    <th>计划名称</th>
                    <th>时长(月)</th>
                    <th>原价</th>
                    <th>现价</th>
                    <th>包含积分</th>
                    <th>积分有效期(天)</th>
                    <th>状态</th>
                  </tr>
                </thead>
                <tbody>
                  {subscriptionPlans.map((plan) => (
                    <tr key={plan.id}>
                      <td>
                        <Badge variant={
                          plan.plan_type === 'free' ? 'outline' :
                          plan.plan_type === 'pro' ? 'secondary' : 'default'
                        }>
                          {plan.plan_type.toUpperCase()}
                        </Badge>
                      </td>
                      <td>
                        <input
                          type="text"
                          value={plan.plan_name}
                          onChange={(e) => updateSubscriptionPlan(plan.id, 'plan_name', e.target.value)}
                          className="w-full px-2 py-1 border border-input rounded bg-background text-foreground"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={plan.duration_months}
                          onChange={(e) => updateSubscriptionPlan(plan.id, 'duration_months', parseInt(e.target.value) || 1)}
                          className="w-16 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="1"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          step="0.01"
                          value={plan.original_price}
                          onChange={(e) => updateSubscriptionPlan(plan.id, 'original_price', parseFloat(e.target.value) || 0)}
                          className="w-20 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          step="0.01"
                          value={plan.discount_price}
                          onChange={(e) => updateSubscriptionPlan(plan.id, 'discount_price', parseFloat(e.target.value) || 0)}
                          className="w-20 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={plan.points_included}
                          onChange={(e) => updateSubscriptionPlan(plan.id, 'points_included', parseInt(e.target.value) || 0)}
                          className="w-20 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={plan.points_validity_days}
                          onChange={(e) => updateSubscriptionPlan(plan.id, 'points_validity_days', parseInt(e.target.value) || 30)}
                          className="w-16 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="1"
                        />
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          checked={plan.is_active}
                          onChange={(e) => updateSubscriptionPlan(plan.id, 'is_active', e.target.checked)}
                          className="rounded"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        {/* 充值套餐设置 */}
        <TabsContent value="recharge" className="space-y-6">
          <div className="admin-card">
            <div className="p-6 border-b border-border">
              <h3 className="text-lg font-semibold flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                充值套餐设置
              </h3>
            </div>
            <div className="overflow-x-auto">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>套餐标识</th>
                    <th>套餐名称</th>
                    <th>积分数量</th>
                    <th>原价</th>
                    <th>现价</th>
                    <th>赠送积分</th>
                    <th>描述</th>
                    <th>状态</th>
                    <th>排序</th>
                  </tr>
                </thead>
                <tbody>
                  {pointsPackages.map((pkg) => (
                    <tr key={pkg.id}>
                      <td className="font-mono">{pkg.package_key}</td>
                      <td>
                        <input
                          type="text"
                          value={pkg.package_name}
                          onChange={(e) => updatePointsPackage(pkg.id, 'package_name', e.target.value)}
                          className="w-full px-2 py-1 border border-input rounded bg-background text-foreground"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={pkg.points_amount}
                          onChange={(e) => updatePointsPackage(pkg.id, 'points_amount', parseInt(e.target.value) || 0)}
                          className="w-20 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          step="0.01"
                          value={pkg.original_price}
                          onChange={(e) => updatePointsPackage(pkg.id, 'original_price', parseFloat(e.target.value) || 0)}
                          className="w-20 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          step="0.01"
                          value={pkg.discount_price}
                          onChange={(e) => updatePointsPackage(pkg.id, 'discount_price', parseFloat(e.target.value) || 0)}
                          className="w-20 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={pkg.bonus_points}
                          onChange={(e) => updatePointsPackage(pkg.id, 'bonus_points', parseInt(e.target.value) || 0)}
                          className="w-20 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                      <td>
                        <input
                          type="text"
                          value={pkg.description || ''}
                          onChange={(e) => updatePointsPackage(pkg.id, 'description', e.target.value)}
                          className="w-full px-2 py-1 border border-input rounded bg-background text-foreground"
                          placeholder="套餐描述"
                        />
                      </td>
                      <td>
                        <input
                          type="checkbox"
                          checked={pkg.is_active}
                          onChange={(e) => updatePointsPackage(pkg.id, 'is_active', e.target.checked)}
                          className="rounded"
                        />
                      </td>
                      <td>
                        <input
                          type="number"
                          value={pkg.display_order}
                          onChange={(e) => updatePointsPackage(pkg.id, 'display_order', parseInt(e.target.value) || 999)}
                          className="w-16 px-2 py-1 border border-input rounded bg-background text-foreground"
                          min="0"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        {/* 邀请设置 */}
        <TabsContent value="invitation" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="admin-card">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  邀请好友设置
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">启用邀请奖励</label>
                  <input
                    type="checkbox"
                    checked={getConfigValue('invitation_enabled') === '1'}
                    onChange={(e) => updateConfig('invitation_enabled', e.target.checked ? '1' : '0')}
                    className="rounded"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">每次邀请奖励积分</label>
                  <input
                    type="number"
                    value={getConfigValue('invitation_points_per_user', '100')}
                    onChange={(e) => updateConfig('invitation_points_per_user', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                    min="0"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">邀请积分有效期（天）</label>
                  <input
                    type="number"
                    value={getActivityPointsConfig().invitation?.validity_days || 15}
                    onChange={(e) => updateActivityPointsConfig('invitation', parseInt(e.target.value))}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                    min="1"
                    max="999"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    邀请奖励积分的有效期，过期后积分将自动失效
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">每个用户最多邀请数量</label>
                  <input
                    type="number"
                    value={getConfigValue('max_invitations_per_user', '10')}
                    onChange={(e) => updateConfig('max_invitations_per_user', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                    min="1"
                  />
                </div>
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>计算说明：</strong>
                    <br />
                    最多可获得邀请积分 = {getConfigValue('invitation_points_per_user', '100')} × {getConfigValue('max_invitations_per_user', '10')} = {parseInt(getConfigValue('invitation_points_per_user', '100')) * parseInt(getConfigValue('max_invitations_per_user', '10'))} 积分
                    <br />
                    <strong>有效期：</strong>{getActivityPointsConfig().invitation?.validity_days || 15} 天
                  </div>
                </div>
              </div>
            </div>

            <div className="admin-card">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  邀请码设置
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div>
                  <label className="text-sm font-medium">邀请码长度</label>
                  <input
                    type="number"
                    value={getConfigValue('invitation_code_length', '8')}
                    onChange={(e) => updateConfig('invitation_code_length', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                    min="4"
                    max="20"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">邀请码过期天数</label>
                  <input
                    type="number"
                    value={getConfigValue('invitation_expire_days', '30')}
                    onChange={(e) => updateConfig('invitation_expire_days', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                    min="1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    邀请码本身的有效期，与积分有效期不同
                  </p>
                </div>
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-sm text-green-800 dark:text-green-200">
                    <strong>邀请机制：</strong>每个用户都有唯一的邀请码，成功邀请新用户注册后获得积分奖励。
                    <br />
                    <strong>区别说明：</strong>
                    <br />
                    • 邀请码过期天数：邀请链接的有效期
                    <br />
                    • 邀请积分有效期：获得积分的使用期限
                  </div>
                </div>
              </div>
            </div>

            {/* 活动积分有效期配置 */}
            <div className="admin-card lg:col-span-2">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold flex items-center">
                  <Activity className="h-5 w-5 mr-2" />
                  活动积分有效期配置
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  为不同类型的活动积分设置独立的有效期
                </p>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {Object.entries(getActivityPointsConfig()).map(([activityType, config]: [string, any]) => (
                    <div key={activityType} className="p-4 border border-border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">
                          {config.description || activityType}
                        </h4>
                        <Badge variant="outline">
                          {activityType}
                        </Badge>
                      </div>
                      <div>
                        <label className="text-sm font-medium">有效期（天）</label>
                        <input
                          type="number"
                          value={config.validity_days || 30}
                          onChange={(e) => updateActivityPointsConfig(activityType, parseInt(e.target.value))}
                          className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                          min="1"
                          max="999"
                        />
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                  <div className="text-sm text-amber-800 dark:text-amber-200">
                    <strong>配置说明：</strong>
                    <br />
                    • <strong>邀请活动积分：</strong>通过邀请好友注册获得的积分
                    <br />
                    • <strong>注册奖励积分：</strong>新用户注册时获得的积分
                    <br />
                    • <strong>特殊活动积分：</strong>特殊活动或促销获得的积分
                    <br />
                    <strong>注意：</strong>修改有效期只影响新获得的积分，已有积分的有效期不会改变
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
