@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 7%;
    --foreground: 0 0% 98%;
    --card: 0 0% 9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14%;
    --muted-foreground: 0 0% 63%;
    --accent: 0 0% 14%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14%;
    --input: 0 0% 14%;
    --ring: 0 0% 83%;
  }
}

@layer components {
  /* 拖拽相关样式 */
  .drag-item {
    @apply cursor-move transition-all duration-200 ease-in-out;
  }
  
  .drag-item:hover {
    @apply shadow-md border-primary/50;
  }
  
  .drag-item.dragging {
    @apply opacity-50 scale-95 border-primary shadow-lg;
  }
  
  .drag-item.drag-over {
    @apply border-primary border-2 bg-primary/5;
  }
  
  .drag-item.drag-over::before {
    content: '';
    @apply absolute inset-0 bg-primary/10 rounded-lg pointer-events-none;
  }
  
  /* 拖拽指示器动画 */
  .drag-handle {
    @apply opacity-0 transition-opacity duration-200;
  }
  
  .drag-item:hover .drag-handle {
    @apply opacity-100;
  }
  
  /* 顺序指示器样式 */
  .order-badge {
    @apply px-2 py-1 bg-primary/10 text-primary text-xs rounded-full font-mono;
  }
  
  /* 加载指示器 */
  .loading-overlay {
    @apply absolute inset-0 bg-background/50 flex items-center justify-center rounded-lg;
  }
  
  /* 通知样式 */
  .notification {
    @apply fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center space-x-2;
  }
  
  .notification.success {
    @apply bg-green-500 text-white;
  }
  
  .notification.error {
    @apply bg-red-500 text-white;
  }
  
  .notification.info {
    @apply bg-blue-500 text-white;
  }
  
  /* 平滑动画 */
  .smooth-transition {
    @apply transition-all duration-300 ease-in-out;
  }
  
  /* 卡片悬停效果 */
  .card-hover {
    @apply transition-all duration-200 hover:shadow-md hover:border-primary/50;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Admin specific styles */
.admin-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
}

.admin-card {
  @apply bg-card border border-border rounded-lg shadow-sm;
}

.admin-stats-card {
  @apply admin-card p-6 hover:shadow-md transition-shadow duration-200;
}

.admin-table {
  @apply w-full border-collapse;
}

.admin-table th {
  @apply px-4 py-3 text-left text-sm font-medium text-muted-foreground border-b border-border;
}

.admin-table td {
  @apply px-4 py-3 text-sm border-b border-border;
}

.admin-table tr:hover {
  @apply bg-muted/50;
}

/* Loading animations */
.loading-spinner {
  @apply animate-spin rounded-full border-2 border-primary border-t-transparent;
}

.loading-pulse {
  @apply animate-pulse bg-muted rounded;
}

/* Status badges */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-badge.active {
  @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
}

.status-badge.inactive {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400;
}

.status-badge.deployed {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
}

.status-badge.pending {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400;
}

.status-badge.error {
  @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
} 