import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30d'

    const stats = await adminQueries.getModelStats(period)
    return NextResponse.json(stats)
  } catch (error) {
    console.error('Failed to fetch model stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 