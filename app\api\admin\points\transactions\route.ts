import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const options = {
      limit: parseInt(searchParams.get('limit') || '50'),
      offset: parseInt(searchParams.get('offset') || '0'),
      transactionType: searchParams.get('transactionType') || 'all',
      sourceType: searchParams.get('sourceType') || 'all',
      pointsType: searchParams.get('pointsType') || 'all',
      dateRange: searchParams.get('dateRange') || '30d',
      search: searchParams.get('search') || '',
      sortBy: searchParams.get('sortBy') || 'created_at',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    }
    
    const transactions = await adminQueries.getPointsTransactions(options)
    return NextResponse.json(transactions)
  } catch (error) {
    console.error('Failed to fetch points transactions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
