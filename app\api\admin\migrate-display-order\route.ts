import { NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database'

export async function POST() {
  try {
    console.log('🚀 开始数据库迁移：添加display_order字段...')
    
    // 1. 检查字段是否已存在
    try {
      await executeQuery(`
        SELECT display_order FROM community_projects LIMIT 1
      `)
      return NextResponse.json({
        success: true,
        message: 'display_order字段已存在，无需迁移'
      })
    } catch (error: any) {
      if (error.code !== 'ER_BAD_FIELD_ERROR') {
        throw error
      }
      // 字段不存在，继续迁移
    }
    
    // 2. 添加display_order字段
    await executeQuery(`
      ALTER TABLE community_projects 
      ADD COLUMN display_order INT DEFAULT 999999 AFTER html_content
    `)
    console.log('✅ 成功添加display_order字段')
    
    // 3. 为现有项目设置初始顺序（按创建时间）
    await executeQuery(`
      SET @row_number = 0
    `)
    
    await executeQuery(`
      UPDATE community_projects 
      SET display_order = (@row_number := @row_number + 1)
      ORDER BY created_at ASC
    `)
    console.log('✅ 成功设置初始顺序')
    
    // 4. 添加索引以提高查询性能
    try {
      await executeQuery(`
        CREATE INDEX idx_community_projects_display_order ON community_projects(display_order)
      `)
      console.log('✅ 成功添加索引')
    } catch (error: any) {
      if (error.code !== 'ER_DUP_KEYNAME') {
        console.log('⚠️ 添加索引失败:', error.message)
      }
    }
    
    // 5. 验证迁移结果
    const result = await executeQuery(`
      SELECT COUNT(*) as total_projects, 
             MIN(display_order) as min_order, 
             MAX(display_order) as max_order
      FROM community_projects
    `) as any[]
    
    console.log('📊 迁移结果:', result[0])
    
    return NextResponse.json({
      success: true,
      message: '数据库迁移成功完成',
      stats: result[0]
    })
    
  } catch (error: any) {
    console.error('❌ 数据库迁移失败:', error)
    return NextResponse.json(
      { error: '数据库迁移失败', details: error.message },
      { status: 500 }
    )
  }
} 