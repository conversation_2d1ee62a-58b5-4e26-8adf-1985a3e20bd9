import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') as 'users' | 'transactions'
    
    if (!type || !['users', 'transactions'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid export type' },
        { status: 400 }
      )
    }
    
    const data = await adminQueries.exportPointsData(type)
    
    // 生成CSV内容
    let headers: string[]
    let csvContent: string
    
    if (type === 'users') {
      headers = [
        '用户ID',
        '昵称',
        '手机号',
        '当前积分',
        '总获得积分',
        '总消费积分',
        '注册时间',
        '最后交易时间'
      ]
      
      csvContent = [
        headers.join(','),
        ...data.map((item: any) => [
          item.id,
          `"${item.nickname || ''}"`,
          `"${item.phone || ''}"`,
          item.points,
          item.total_earned_points,
          item.total_spent_points,
          `"${new Date(item.created_at).toLocaleString('zh-CN')}"`,
          `"${item.last_transaction_at ? new Date(item.last_transaction_at).toLocaleString('zh-CN') : '无交易记录'}"`
        ].join(','))
      ].join('\n')
    } else {
      headers = [
        '交易ID',
        '用户ID',
        '用户昵称',
        '交易类型',
        '积分变动',
        '变动前余额',
        '变动后余额',
        '来源类型',
        '积分类型',
        '说明',
        '交易时间'
      ]
      
      csvContent = [
        headers.join(','),
        ...data.map((item: any) => [
          item.id,
          item.user_id,
          `"${item.user_name || ''}"`,
          item.transaction_type === 'earn' ? '获得' : '消费',
          item.points_amount,
          item.balance_before,
          item.balance_after,
          item.source_type,
          item.points_type || '',
          `"${item.description || ''}"`,
          `"${new Date(item.created_at).toLocaleString('zh-CN')}"`
        ].join(','))
      ].join('\n')
    }
    
    // 添加BOM以支持中文
    const bom = '\uFEFF'
    const csvWithBom = bom + csvContent
    
    return new NextResponse(csvWithBom, {
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="points_${type}_export_${new Date().toISOString().split('T')[0]}.csv"`
      }
    })
  } catch (error) {
    console.error('Failed to export points data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
