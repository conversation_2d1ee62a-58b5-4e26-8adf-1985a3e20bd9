import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    
    const projects = await adminQueries.getCommunityProjects(limit, offset)
    return NextResponse.json(projects)
  } catch (error) {
    console.error('Failed to fetch community projects:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { projectId, newOrder } = await request.json()
    
    if (!projectId || newOrder === undefined) {
      return NextResponse.json(
        { error: 'projectId and newOrder are required' },
        { status: 400 }
      )
    }
    
    await adminQueries.updateCommunityProjectOrder(projectId, newOrder)
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Failed to update community project order:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 