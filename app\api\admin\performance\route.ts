import { NextResponse } from 'next/server'
import PerformanceMonitor from '@/lib/performance-monitor'

export async function GET() {
  try {
    const monitor = PerformanceMonitor.getInstance()
    
    const stats = monitor.getPerformanceStats()
    const slowQueries = monitor.getSlowQueries(20)
    const queryFrequency = monitor.getQueryFrequency()
    
    return NextResponse.json({
      stats,
      slowQueries,
      queryFrequency,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Failed to get performance stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE() {
  try {
    const monitor = PerformanceMonitor.getInstance()
    monitor.clearLogs()
    
    return NextResponse.json({ 
      success: true, 
      message: '性能日志已清除' 
    })
  } catch (error) {
    console.error('Failed to clear performance logs:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
