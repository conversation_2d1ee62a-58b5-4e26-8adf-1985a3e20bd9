"use client"

import { useEffect, useState } from 'react'
import { Activity, Search, Filter, RefreshCw } from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface LogEntry {
  type: string
  target_name: string
  user_name: string
  timestamp: string
  target_id: number
  user_id: number
}

export default function LogsPage() {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')

  useEffect(() => {
    fetchLogs()
  }, [])

  const fetchLogs = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/activities?limit=100')
      if (response.ok) {
        const data = await response.json()
        setLogs(data)
      }
    } catch (error) {
      console.error('Failed to fetch logs:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.target_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.user_name?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterType === 'all' || log.type === filterType
    return matchesSearch && matchesFilter
  })

  const getLogTypeColor = (type: string) => {
    switch (type) {
      case 'user_registered':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'project_created':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      case 'project_deployed':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
      case 'community_shared':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const getLogTypeText = (type: string) => {
    switch (type) {
      case 'user_registered':
        return '用户注册'
      case 'project_created':
        return '项目创建'
      case 'project_deployed':
        return '项目部署'
      case 'community_shared':
        return '社区分享'
      default:
        return type
    }
  }

  const getLogDescription = (log: LogEntry) => {
    switch (log.type) {
      case 'user_registered':
        return `用户 ${log.user_name} 注册了账号`
      case 'project_created':
        return `${log.user_name} 创建了项目 "${log.target_name}"`
      case 'project_deployed':
        return `${log.user_name} 部署了项目 "${log.target_name}"`
      case 'community_shared':
        return `${log.user_name} 分享了项目 "${log.target_name}" 到社区`
      default:
        return `${log.user_name} 执行了 ${log.type} 操作`
    }
  }

  if (loading) {
    return (
      <div className="flex-1 p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-64 mb-8"></div>
          <div className="admin-card">
            <div className="p-6">
              <div className="h-6 bg-muted rounded w-48 mb-4"></div>
              <div className="space-y-4">
                {[...Array(20)].map((_, i) => (
                  <div key={i} className="h-16 bg-muted rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold flex items-center">
          <Activity className="mr-3 h-8 w-8" />
          系统日志
        </h1>
        <p className="text-muted-foreground mt-2">
          查看系统活动和用户操作记录
        </p>
      </div>

      <div className="admin-card">
        {/* 搜索和过滤 */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="搜索日志..."
                  className="pl-10 pr-4 py-2 border border-input rounded-md bg-background text-foreground"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <select
                  className="border border-input rounded-md px-3 py-2 bg-background text-foreground"
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                >
                  <option value="all">全部类型</option>
                  <option value="user_registered">用户注册</option>
                  <option value="project_created">项目创建</option>
                  <option value="project_deployed">项目部署</option>
                  <option value="community_shared">社区分享</option>
                </select>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                onClick={fetchLogs}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>刷新</span>
              </Button>
              <div className="text-sm text-muted-foreground">
                共 {filteredLogs.length} 条日志
              </div>
            </div>
          </div>
        </div>

        {/* 日志列表 */}
        <div className="divide-y divide-border">
          {filteredLogs.map((log, index) => (
            <div key={index} className="p-6 hover:bg-muted/50 transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className="mt-1">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getLogTypeColor(log.type)}`}>
                      {getLogTypeText(log.type)}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground mb-1">
                      {getLogDescription(log)}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span>用户ID: {log.user_id}</span>
                      {log.target_id && <span>目标ID: {log.target_id}</span>}
                      <span>{formatDate(log.timestamp)}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      // TODO: 实现查看详情
                      console.log('查看详情:', log)
                    }}
                  >
                    详情
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredLogs.length === 0 && (
          <div className="p-12 text-center text-muted-foreground">
            <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">暂无日志记录</p>
            <p className="text-sm">
              {searchTerm || filterType !== 'all' ? '没有找到匹配的日志' : '系统中还没有活动日志'}
            </p>
          </div>
        )}
      </div>

      {/* 日志统计 */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="admin-stats-card">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/20">
              <Activity className="h-5 w-5 text-green-600" />
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold">
              {logs.filter(log => log.type === 'user_registered').length}
            </p>
            <p className="text-sm text-muted-foreground">用户注册</p>
          </div>
        </div>

        <div className="admin-stats-card">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <Activity className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold">
              {logs.filter(log => log.type === 'project_created').length}
            </p>
            <p className="text-sm text-muted-foreground">项目创建</p>
          </div>
        </div>

        <div className="admin-stats-card">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/20">
              <Activity className="h-5 w-5 text-purple-600" />
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold">
              {logs.filter(log => log.type === 'project_deployed').length}
            </p>
            <p className="text-sm text-muted-foreground">项目部署</p>
          </div>
        </div>

        <div className="admin-stats-card">
          <div className="flex items-center justify-between mb-4">
            <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/20">
              <Activity className="h-5 w-5 text-orange-600" />
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold">
              {logs.filter(log => log.type === 'community_shared').length}
            </p>
            <p className="text-sm text-muted-foreground">社区分享</p>
          </div>
        </div>
      </div>
    </div>
  )
} 