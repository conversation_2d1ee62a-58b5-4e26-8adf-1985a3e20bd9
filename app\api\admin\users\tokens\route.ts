import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30d'

    const userStats = await adminQueries.getUserTokenStats(period)

    return NextResponse.json(userStats)
  } catch (error) {
    console.error('获取用户Token统计失败:', error)
    return NextResponse.json(
      { error: '获取用户Token统计失败' },
      { status: 500 }
    )
  }
} 