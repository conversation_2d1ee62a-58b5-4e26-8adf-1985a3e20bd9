import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET() {
  try {
    const plans = await adminQueries.getSubscriptionPlans()
    return NextResponse.json(plans)
  } catch (error) {
    console.error('Failed to fetch subscription plans:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { plans } = await request.json()
    
    if (!Array.isArray(plans)) {
      return NextResponse.json(
        { error: 'Invalid plans format' },
        { status: 400 }
      )
    }

    await adminQueries.updateSubscriptionPlans(plans)
    
    return NextResponse.json({ 
      success: true, 
      message: '订阅计划配置更新成功' 
    })
  } catch (error) {
    console.error('Failed to update subscription plans:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
