"use client"

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'
import {
  LayoutDashboard,
  Users,
  FolderOpen,
  Globe,
  Activity,
  Settings,
  Moon,
  Sun,
  Monitor,
  BarChart3,
  Coins,
  Sliders
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

const navigation = [
  {
    name: '仪表盘',
    href: '/',
    icon: LayoutDashboard,
  },
  {
    name: '用户管理',
    href: '/users',
    icon: Users,
  },
  {
    name: '积分管理',
    href: '/points',
    icon: Coins,
  },
  {
    name: '项目管理',
    href: '/projects',
    icon: FolderOpen,
  },
  {
    name: '社区管理',
    href: '/community',
    icon: Globe,
  },
  {
    name: '模型统计',
    href: '/models',
    icon: BarChart3,
  },
  {
    name: '系统日志',
    href: '/logs',
    icon: Activity,
  },
  {
    name: '系统参数',
    href: '/system-config',
    icon: Sliders,
  },
  {
    name: '系统设置',
    href: '/settings',
    icon: Settings,
  },
]

export function AdminNavigation() {
  const pathname = usePathname()
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // 确保组件已挂载，避免水合错误
  useEffect(() => {
    setMounted(true)
  }, [])

  // 在组件挂载前不渲染主题相关的图标
  if (!mounted) {
    return (
      <div className="flex h-screen w-64 flex-col bg-card border-r border-border fixed left-0 top-0 z-50">
        {/* Logo */}
        <div className="flex h-16 items-center justify-between px-6 border-b border-border">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">LR</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold">LoomRun</h1>
              <p className="text-xs text-muted-foreground">管理后台</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-1 px-3 py-4">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`
                  flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  ${isActive 
                    ? 'bg-primary text-primary-foreground' 
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  }
                `}
              >
                <item.icon className="mr-3 h-4 w-4" />
                {item.name}
              </Link>
            )
          })}
        </nav>

        {/* Theme Toggle & Footer - 加载状态 */}
        <div className="border-t border-border p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">主题</span>
            <Button variant="outline" size="sm" disabled>
              <div className="h-4 w-4 animate-pulse bg-muted-foreground/20 rounded" />
            </Button>
          </div>
          <div className="mt-4 text-xs text-muted-foreground">
            <p>LoomRun Admin v1.0</p>
            <p>© 2024 LoomRun</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen w-64 flex-col bg-card border-r border-border fixed left-0 top-0 z-50">
      {/* Logo */}
      <div className="flex h-16 items-center justify-between px-6 border-b border-border">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">LR</span>
          </div>
          <div>
            <h1 className="text-lg font-semibold">LoomRun</h1>
            <p className="text-xs text-muted-foreground">管理后台</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`
                flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                ${isActive 
                  ? 'bg-primary text-primary-foreground' 
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                }
              `}
            >
              <item.icon className="mr-3 h-4 w-4" />
              {item.name}
            </Link>
          )
        })}
      </nav>

      {/* Theme Toggle & Footer */}
      <div className="border-t border-border p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">主题</span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                {theme === 'dark' ? <Moon className="h-4 w-4" /> : 
                 theme === 'light' ? <Sun className="h-4 w-4" /> : 
                 <Monitor className="h-4 w-4" />}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setTheme('light')}>
                <Sun className="mr-2 h-4 w-4" />
                <span>浅色</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('dark')}>
                <Moon className="mr-2 h-4 w-4" />
                <span>深色</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('system')}>
                <Monitor className="mr-2 h-4 w-4" />
                <span>系统</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="mt-4 text-xs text-muted-foreground">
          <p>LoomRun Admin v1.0</p>
          <p>© 2024 LoomRun</p>
        </div>
      </div>
    </div>
  )
} 