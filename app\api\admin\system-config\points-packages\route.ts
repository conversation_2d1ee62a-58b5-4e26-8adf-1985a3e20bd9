import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET() {
  try {
    const packages = await adminQueries.getPointsPackages()
    return NextResponse.json(packages)
  } catch (error) {
    console.error('Failed to fetch points packages:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { packages } = await request.json()
    
    if (!Array.isArray(packages)) {
      return NextResponse.json(
        { error: 'Invalid packages format' },
        { status: 400 }
      )
    }

    await adminQueries.updatePointsPackages(packages)
    
    return NextResponse.json({ 
      success: true, 
      message: '充值套餐配置更新成功' 
    })
  } catch (error) {
    console.error('Failed to update points packages:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
