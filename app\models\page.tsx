"use client"

import { useEffect, useState } from 'react'
import { BarChart3, Activity, Zap, DollarSign, TrendingUp, Users, Calendar, Filter } from 'lucide-react'
import { formatNumber, formatDate } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface ModelStats {
  model: string
  requests: number
  tokens: number
  cost: number
}

interface UsageStats {
  totalRequests: number
  totalTokens: number
  totalCost: number
  averageTokensPerRequest: number
}

interface UserTokenStats {
  user_id: number
  nickname: string
  phone: string
  total_requests: number
  total_tokens: number
  total_cost: number
  membership_type: string
  active_days: number
}

export default function ModelsPage() {
  const [modelStats, setModelStats] = useState<ModelStats[]>([])
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null)
  const [userStats, setUserStats] = useState<UserTokenStats[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('30d')
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // 从API获取真实数据
        const [modelResponse, userResponse] = await Promise.all([
          fetch(`/api/admin/models?period=${selectedPeriod}`),
          fetch(`/api/admin/users/tokens?period=${selectedPeriod}`)
        ])
        
        let mockModelStats: ModelStats[] = []
        let mockUserStats: UserTokenStats[] = []
        
        if (modelResponse.ok) {
          mockModelStats = await modelResponse.json()
        }
        
        if (userResponse.ok) {
          mockUserStats = await userResponse.json()
        }
        
        // 确保数据格式正确
        if (mockModelStats.length === 0) {
          mockModelStats = [
            { model: 'deepseek-chat', requests: 0, tokens: 0, cost: 0 },
            { model: 'doubao-seed-1-6-250615', requests: 0, tokens: 0, cost: 0 },
          ]
        }

        const totalRequests = mockModelStats.reduce((sum, stat) => sum + (Number(stat.requests) || 0), 0)
        const totalTokens = mockModelStats.reduce((sum, stat) => sum + (Number(stat.tokens) || 0), 0)
        const totalCost = mockModelStats.reduce((sum, stat) => sum + (Number(stat.cost) || 0), 0)

        setModelStats(mockModelStats)
        setUserStats(mockUserStats)
        setUsageStats({
          totalRequests,
          totalTokens,
          totalCost,
          averageTokensPerRequest: totalRequests > 0 ? totalTokens / totalRequests : 0
        })
      } catch (error) {
        console.error('Failed to fetch model stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [selectedPeriod])

  if (loading) {
    return (
      <div className="flex-1 p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-64 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="admin-card p-6">
                <div className="h-4 bg-muted rounded w-20 mb-4"></div>
                <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                <div className="h-3 bg-muted rounded w-24"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const getPeriodText = (period: string) => {
    switch (period) {
      case '1d': return '今天'
      case '7d': return '本周'
      case '30d': return '本月'
      case '1y': return '今年'
      default: return '本月'
    }
  }

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              <BarChart3 className="mr-3 h-8 w-8" />
              模型统计
            </h1>
            <p className="text-muted-foreground mt-2">
              AI模型使用情况和Token消耗统计
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="border border-input rounded-md px-3 py-2 bg-background text-foreground"
              >
                <option value="1d">今天</option>
                <option value="7d">本周</option>
                <option value="30d">本月</option>
                <option value="1y">今年</option>
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <select
                value={activeTab}
                onChange={(e) => setActiveTab(e.target.value)}
                className="border border-input rounded-md px-3 py-2 bg-background text-foreground"
              >
                <option value="overview">概览</option>
                <option value="users">用户统计</option>
                <option value="models">模型详情</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* 总体统计 */}
      {usageStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
                <Activity className="h-5 w-5 text-blue-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(usageStats.totalRequests)}</p>
              <p className="text-sm text-muted-foreground">总请求数</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/20">
                <Zap className="h-5 w-5 text-green-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(usageStats.totalTokens)}</p>
              <p className="text-sm text-muted-foreground">总Token数</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/20">
                <DollarSign className="h-5 w-5 text-orange-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <p className="text-2xl font-bold">¥{(Number(usageStats.totalCost) || 0).toFixed(2)}</p>
              <p className="text-sm text-muted-foreground">总成本</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/20">
                <BarChart3 className="h-5 w-5 text-purple-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <p className="text-2xl font-bold">{Math.round(usageStats.averageTokensPerRequest)}</p>
              <p className="text-sm text-muted-foreground">平均Token/请求</p>
            </div>
          </div>
        </div>
      )}

      {/* 模型详细统计 */}
      <div className="admin-card">
        <div className="p-6 border-b border-border">
          <h3 className="text-lg font-semibold">模型使用详情</h3>
        </div>
        <div className="p-6">
          <div className="space-y-6">
            {modelStats.map((stat, index) => {
              const requestPercentage = usageStats ? (stat.requests / usageStats.totalRequests) * 100 : 0
              const tokenPercentage = usageStats ? (stat.tokens / usageStats.totalTokens) * 100 : 0
              const avgTokensPerRequest = stat.tokens / stat.requests

              return (
                <div key={stat.model} className="border border-border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        index === 0 ? 'bg-blue-500' : 
                        index === 1 ? 'bg-green-500' : 
                        'bg-orange-500'
                      }`}></div>
                      <h4 className="font-semibold text-lg">{stat.model}</h4>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-muted-foreground">平均Token/请求</p>
                      <p className="font-medium">{Math.round(avgTokensPerRequest)}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* 请求数统计 */}
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-sm text-muted-foreground">请求数</span>
                        <span className="text-sm font-medium">{formatNumber(stat.requests)} ({requestPercentage.toFixed(1)}%)</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            index === 0 ? 'bg-blue-500' : 
                            index === 1 ? 'bg-green-500' : 
                            'bg-orange-500'
                          }`}
                          style={{ width: `${requestPercentage}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Token数统计 */}
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-sm text-muted-foreground">Token数</span>
                        <span className="text-sm font-medium">{formatNumber(stat.tokens)} ({tokenPercentage.toFixed(1)}%)</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            index === 0 ? 'bg-blue-500' : 
                            index === 1 ? 'bg-green-500' : 
                            'bg-orange-500'
                          }`}
                          style={{ width: `${tokenPercentage}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  {/* 成本计算 */}
                  <div className="mt-4 pt-4 border-t border-border">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">估算成本</span>
                      <span className="font-medium">¥{(stat.tokens * 0.00003).toFixed(4)}</span>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* 用户Token统计 */}
      {activeTab === 'users' && (
        <div className="mt-8 admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold flex items-center">
              <Users className="mr-2 h-5 w-5" />
              用户Token使用排行 ({getPeriodText(selectedPeriod)})
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="admin-table">
              <thead>
                <tr>
                  <th>排名</th>
                  <th>用户</th>
                  <th>会员类型</th>
                  <th>请求数</th>
                  <th>Token数</th>
                  <th>成本</th>
                  <th>活跃天数</th>
                  <th>平均Token/请求</th>
                </tr>
              </thead>
              <tbody>
                {userStats.map((user, index) => (
                  <tr key={user.user_id}>
                    <td className="font-mono">#{index + 1}</td>
                    <td>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {user.nickname || `用户${user.user_id}`}
                        </span>
                        <span className="text-xs text-muted-foreground font-mono">
                          ID: {user.user_id}
                        </span>
                      </div>
                    </td>
                    <td>
                      <span className={`status-badge ${
                        user.membership_type === 'pro' ? 'deployed' : 
                        user.membership_type === 'max' ? 'active' : 'pending'
                      }`}>
                        {user.membership_type === 'pro' ? 'Pro' : 
                         user.membership_type === 'max' ? 'Max' : 'Free'}
                      </span>
                    </td>
                    <td className="font-medium text-blue-600">
                      {formatNumber(user.total_requests)}
                    </td>
                    <td className="font-medium text-green-600">
                      {formatNumber(user.total_tokens)}
                    </td>
                    <td className="font-medium text-orange-600">
                      ¥{user.total_cost.toFixed(4)}
                    </td>
                    <td className="font-medium">
                      {user.active_days}天
                    </td>
                    <td className="font-mono">
                      {user.total_requests > 0 ? 
                        Math.round(user.total_tokens / user.total_requests) : 0
                      }
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {userStats.length === 0 && (
              <div className="p-12 text-center text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">暂无用户数据</p>
                <p className="text-sm">
                  {getPeriodText(selectedPeriod)}没有用户使用Token
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 模型详情 */}
      {activeTab === 'models' && (
        <div className="mt-8 admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold">模型使用详情 ({getPeriodText(selectedPeriod)})</h3>
          </div>
          <div className="p-6">
            <div className="space-y-6">
              {modelStats.map((stat, index) => {
                const requestPercentage = usageStats ? (stat.requests / usageStats.totalRequests) * 100 : 0
                const tokenPercentage = usageStats ? (stat.tokens / usageStats.totalTokens) * 100 : 0
                const avgTokensPerRequest = stat.requests > 0 ? stat.tokens / stat.requests : 0

                return (
                  <div key={stat.model} className="border border-border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          index === 0 ? 'bg-blue-500' : 'bg-green-500'
                        }`}></div>
                        <h4 className="font-semibold text-lg">{stat.model}</h4>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-muted-foreground">平均Token/请求</p>
                        <p className="font-medium">{Math.round(avgTokensPerRequest)}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* 请求数统计 */}
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm text-muted-foreground">请求数</span>
                          <span className="text-sm font-medium">{formatNumber(stat.requests)} ({requestPercentage.toFixed(1)}%)</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              index === 0 ? 'bg-blue-500' : 'bg-green-500'
                            }`}
                            style={{ width: `${requestPercentage}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* Token数统计 */}
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm text-muted-foreground">Token数</span>
                          <span className="text-sm font-medium">{formatNumber(stat.tokens)} ({tokenPercentage.toFixed(1)}%)</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              index === 0 ? 'bg-blue-500' : 'bg-green-500'
                            }`}
                            style={{ width: `${tokenPercentage}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* 成本统计 */}
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm text-muted-foreground">成本</span>
                          <span className="text-sm font-medium">¥{stat.cost.toFixed(4)}</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              index === 0 ? 'bg-blue-500' : 'bg-green-500'
                            }`}
                            style={{ width: `${usageStats ? (stat.cost / usageStats.totalCost) * 100 : 0}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      )}

      {/* 使用趋势图占位 */}
      {activeTab === 'overview' && (
        <div className="mt-8 admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold">使用趋势 ({getPeriodText(selectedPeriod)})</h3>
          </div>
          <div className="p-6">
            <div className="h-64 flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">趋势图表</p>
                <p className="text-sm">此功能需要集成图表库实现</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 