import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = parseInt(params.id)
    
    if (isNaN(userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      )
    }
    
    const body = await request.json()
    const { is_active } = body
    
    if (typeof is_active !== 'boolean') {
      return NextResponse.json(
        { error: 'is_active must be a boolean' },
        { status: 400 }
      )
    }
    
    await adminQueries.updateUserStatus(userId, is_active)
    
    return NextResponse.json({ 
      success: true, 
      message: `用户已${is_active ? '启用' : '禁用'}` 
    })
  } catch (error) {
    console.error('Failed to update user status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
