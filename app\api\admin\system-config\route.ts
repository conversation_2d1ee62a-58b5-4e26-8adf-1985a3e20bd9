import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET() {
  try {
    const configs = await adminQueries.getSystemConfigs()
    return NextResponse.json(configs)
  } catch (error) {
    console.error('Failed to fetch system configs:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { configs } = await request.json()
    
    if (!Array.isArray(configs)) {
      return NextResponse.json(
        { error: 'Invalid configs format' },
        { status: 400 }
      )
    }

    await adminQueries.updateSystemConfigs(configs)
    
    return NextResponse.json({ 
      success: true, 
      message: '系统配置更新成功' 
    })
  } catch (error) {
    console.error('Failed to update system configs:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
