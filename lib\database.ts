import mysql from 'mysql2/promise';
import PerformanceMonitor from './performance-monitor';

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'loomrun',
  charset: 'utf8mb4',
  timezone: '+08:00',
};

let pool: mysql.Pool | null = null;

export const getPool = () => {
  if (!pool) {
    pool = mysql.createPool({
      ...DB_CONFIG,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      idleTimeout: 300000,
      maxIdle: 10,
    });
  }
  return pool;
};

export const executeQuery = async (query: string, params: unknown[] = []): Promise<unknown> => {
  const connection = getPool();
  let retries = 3;
  const monitor = PerformanceMonitor.getInstance();
  const startTime = Date.now();

  while (retries > 0) {
    try {
      const [results] = await connection.execute(query, params);

      // 记录查询性能
      const duration = Date.now() - startTime;
      monitor.logQuery(query, duration, params);

      return results;
    } catch (error: unknown) {
      console.error('Database query error:', error);
      retries--;
      if (retries === 0) {
        // 记录失败查询
        const duration = Date.now() - startTime;
        monitor.logQuery(`FAILED: ${query}`, duration, params);
        throw error;
      }
      console.log(`Connection error, retrying... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
};

// Admin specific database queries
export const adminQueries = {
  // 获取系统统计信息
  getSystemStats: async () => {
    // 使用单个查询获取所有统计数据，提高性能
    const [stats] = await executeQuery(`
      SELECT
        (SELECT COUNT(*) FROM users) as total_users,
        (SELECT COUNT(*) FROM projects) as total_projects,
        (SELECT COUNT(*) FROM projects WHERE is_deployed = 1) as deployed_projects,
        (SELECT COUNT(*) FROM community_projects) as community_projects,
        (SELECT COUNT(*) FROM chat_history) as total_messages,
        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()) as today_users,
        (SELECT COUNT(*) FROM projects WHERE DATE(created_at) = CURDATE()) as today_projects,
        (SELECT COUNT(*) FROM users WHERE last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as active_users
    `) as [any];

    return {
      totalUsers: stats.total_users,
      totalProjects: stats.total_projects,
      deployedProjects: stats.deployed_projects,
      communityProjects: stats.community_projects,
      totalMessages: stats.total_messages,
      todayUsers: stats.today_users,
      todayProjects: stats.today_projects,
      activeUsers: stats.active_users,
    };
  },

  // 获取用户列表（优化版）
  getUsers: async (options: {
    limit?: number
    offset?: number
    search?: string
    status?: string
    membership?: string
    activity?: string
    sortBy?: string
    sortOrder?: string
  } = {}) => {
    const {
      limit = 50,
      offset = 0,
      search = '',
      status = 'all',
      membership = 'all',
      activity = 'all',
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = options;

    let whereConditions = [];
    let params: any[] = [];

    // 搜索条件
    if (search) {
      whereConditions.push(`(
        u.nickname LIKE ? OR
        u.phone LIKE ? OR
        u.id = ?
      )`);
      params.push(`%${search}%`, `%${search}%`, search);
    }

    // 状态过滤
    if (status !== 'all') {
      whereConditions.push('u.is_active = ?');
      params.push(status === 'active' ? 1 : 0);
    }

    // 活跃度过滤
    if (activity === 'recent') {
      whereConditions.push('u.last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)');
    } else if (activity === 'inactive') {
      whereConditions.push('(u.last_login IS NULL OR u.last_login < DATE_SUB(NOW(), INTERVAL 30 DAY))');
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 排序
    const validSortFields = ['created_at', 'last_login', 'points'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder === 'asc' ? 'ASC' : 'DESC';

    // 优化查询：使用预计算的统计数据
    const users = await executeQuery(`
      SELECT
        u.id,
        u.phone,
        u.nickname,
        u.avatar_url,
        u.created_at,
        u.updated_at,
        u.last_login,
        u.is_active,
        u.points,
        u.total_earned_points,
        u.total_spent_points,
        COALESCE(stats.project_count, 0) as project_count,
        COALESCE(stats.message_count, 0) as message_count,
        COALESCE(stats.community_project_count, 0) as community_project_count,
        COALESCE(membership.membership_type, 'free') as membership_type,
        membership.expires_at as membership_expires_at,
        COALESCE(orders.total_spent, 0) as total_spent
      FROM users u
      LEFT JOIN (
        SELECT
          u2.id as user_id,
          COUNT(DISTINCT p.id) as project_count,
          COUNT(DISTINCT ch.id) as message_count,
          COUNT(DISTINCT cp.id) as community_project_count
        FROM users u2
        LEFT JOIN projects p ON u2.id = p.user_id
        LEFT JOIN chat_history ch ON u2.id = ch.user_id
        LEFT JOIN community_projects cp ON u2.id = cp.user_id
        GROUP BY u2.id
      ) stats ON u.id = stats.user_id
      LEFT JOIN (
        SELECT
          user_id,
          membership_type,
          expires_at
        FROM membership_orders mo
        WHERE mo.status = 'paid'
        AND mo.expires_at > NOW()
        AND mo.id = (
          SELECT MAX(id) FROM membership_orders mo2
          WHERE mo2.user_id = mo.user_id
          AND mo2.status = 'paid'
          AND mo2.expires_at > NOW()
        )
      ) membership ON u.id = membership.user_id
      LEFT JOIN (
        SELECT
          user_id,
          SUM(discount_price) as total_spent
        FROM membership_orders
        WHERE status = 'paid'
        GROUP BY user_id
      ) orders ON u.id = orders.user_id
      ${whereClause}
      ORDER BY u.${sortField} ${order}
      LIMIT ? OFFSET ?
    `, [...params, limit, offset]);

    return users;
  },

  // 获取项目列表
  getProjects: async (limit = 50, offset = 0) => {
    const projects = await executeQuery(`
      SELECT 
        p.id,
        p.title,
        p.user_id,
        p.is_deployed,
        p.deploy_url,
        p.created_at,
        p.updated_at,
        u.nickname as user_name,
        u.phone as user_phone,
        COUNT(DISTINCT pv.id) as version_count,
        COUNT(DISTINCT ch.id) as message_count
      FROM projects p
      LEFT JOIN users u ON p.user_id = u.id
      LEFT JOIN project_versions pv ON p.id = pv.project_id
      LEFT JOIN chat_history ch ON p.id = ch.project_id
      GROUP BY p.id
      ORDER BY p.updated_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `, []);
    
    return projects;
  },

  // 获取社区项目列表
  getCommunityProjects: async (limit = 50, offset = 0) => {
    // 首先尝试使用display_order字段
    try {
      const projects = await executeQuery(`
        SELECT 
          cp.id,
          cp.title,
          cp.user_id,
          cp.original_project_id,
          cp.html_content,
          cp.created_at,
          cp.updated_at,
          u.nickname as user_name,
          u.phone as user_phone,
          p.title as original_title,
          COALESCE(cp.display_order, 999999) as display_order
        FROM community_projects cp
        LEFT JOIN users u ON cp.user_id = u.id
        LEFT JOIN projects p ON cp.original_project_id = p.id
        ORDER BY cp.display_order ASC, cp.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `, []);
      
      return projects;
    } catch (error: any) {
      // 如果display_order字段不存在，使用ROW_NUMBER作为备用
      if (error.code === 'ER_BAD_FIELD_ERROR') {
        console.log('display_order字段不存在，使用备用查询');
        const projects = await executeQuery(`
          SELECT 
            cp.id,
            cp.title,
            cp.user_id,
            cp.original_project_id,
            cp.html_content,
            cp.created_at,
            cp.updated_at,
            u.nickname as user_name,
            u.phone as user_phone,
            p.title as original_title,
            ROW_NUMBER() OVER (ORDER BY cp.created_at DESC) as display_order
          FROM community_projects cp
          LEFT JOIN users u ON cp.user_id = u.id
          LEFT JOIN projects p ON cp.original_project_id = p.id
          ORDER BY cp.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `, []);
        
        return projects;
      }
      throw error;
    }
  },

  // 获取模型使用统计
  getModelStats: async (period = '30d') => {
    let dateCondition = '';
    switch (period) {
      case '1d':
        dateCondition = 'WHERE date >= CURDATE()';
        break;
      case '7d':
        dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';
        break;
      case '30d':
        dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
        break;
      case '1y':
        dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)';
        break;
      default:
        dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
    }

    // 基于 usage_stats 表的真实统计
    const stats = await executeQuery(`
      SELECT 
        'deepseek-chat' as model,
        COALESCE(SUM(requests_count), 0) as requests,
        COALESCE(SUM(requests_count * 100), 0) as tokens,
        COALESCE(SUM(cost_yuan), 0) as cost
      FROM usage_stats
      ${dateCondition}
      UNION ALL
      SELECT 
        'doubao-seed-1-6-250615' as model,
        COUNT(DISTINCT ch.id) as requests,
        COUNT(DISTINCT ch.id) * 80 as tokens,
        COUNT(DISTINCT ch.id) * 0.0024 as cost
      FROM chat_history ch
      ${dateCondition.replace('date >=', 'DATE(ch.created_at) >=')}
        AND ch.message_type = 'ai'
    `, []);
    
    return stats;
  },

  // 获取用户Token使用统计
  getUserTokenStats: async (period = '30d') => {
    let dateCondition = '';
    switch (period) {
      case '1d':
        dateCondition = 'AND us.date >= CURDATE()';
        break;
      case '7d':
        dateCondition = 'AND us.date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';
        break;
      case '30d':
        dateCondition = 'AND us.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
        break;
      case '1y':
        dateCondition = 'AND us.date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)';
        break;
      default:
        dateCondition = 'AND us.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
    }

    const stats = await executeQuery(`
      SELECT 
        u.id as user_id,
        u.nickname,
        u.phone,
        COALESCE(SUM(us.requests_count), 0) as total_requests,
        COALESCE(SUM(us.requests_count * 100), 0) as total_tokens,
        COALESCE(SUM(us.cost_yuan), 0) as total_cost,
        COALESCE(MAX(us.membership_type), 'free') as membership_type,
        COUNT(DISTINCT us.date) as active_days
      FROM users u
      LEFT JOIN usage_stats us ON u.id = us.user_id ${dateCondition}
      GROUP BY u.id, u.nickname, u.phone
      HAVING total_requests > 0
      ORDER BY total_tokens DESC
      LIMIT 50
    `, []);
    
    return stats;
  },

  // 获取Token使用趋势
  getTokenTrends: async (period = '30d') => {
    let dateCondition = '';
    let groupBy = '';
    switch (period) {
      case '7d':
        dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';
        groupBy = 'DATE(date)';
        break;
      case '30d':
        dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
        groupBy = 'DATE(date)';
        break;
      case '1y':
        dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)';
        groupBy = 'DATE_FORMAT(date, "%Y-%m")';
        break;
      default:
        dateCondition = 'WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
        groupBy = 'DATE(date)';
    }

    const trends = await executeQuery(`
      SELECT 
        ${groupBy} as period,
        SUM(requests_count) as requests,
        SUM(requests_count * 100) as tokens,
        SUM(cost_yuan) as cost,
        COUNT(DISTINCT user_id) as active_users
      FROM usage_stats
      ${dateCondition}
      GROUP BY ${groupBy}
      ORDER BY period ASC
    `, []);
    
    return trends;
  },

  // 获取用户统计信息
  getUserStats: async () => {
    const [totalUsers] = await executeQuery('SELECT COUNT(*) as count FROM users') as [{ count: number }];
    const [activeUsers] = await executeQuery('SELECT COUNT(*) as count FROM users WHERE is_active = 1') as [{ count: number }];
    const [newUsersToday] = await executeQuery('SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()') as [{ count: number }];

    // 获取会员用户数
    const [proUsers] = await executeQuery(`
      SELECT COUNT(DISTINCT mo.user_id) as count
      FROM membership_orders mo
      WHERE mo.membership_type = 'pro'
      AND mo.status = 'paid'
      AND mo.expires_at > NOW()
    `) as [{ count: number }];

    const [maxUsers] = await executeQuery(`
      SELECT COUNT(DISTINCT mo.user_id) as count
      FROM membership_orders mo
      WHERE mo.membership_type = 'max'
      AND mo.status = 'paid'
      AND mo.expires_at > NOW()
    `) as [{ count: number }];

    // 获取总收入
    const [totalRevenue] = await executeQuery(`
      SELECT COALESCE(SUM(discount_price), 0) as total
      FROM membership_orders
      WHERE status = 'paid'
    `) as [{ total: number }];

    return {
      totalUsers: totalUsers.count,
      activeUsers: activeUsers.count,
      newUsersToday: newUsersToday.count,
      proUsers: proUsers.count,
      maxUsers: maxUsers.count,
      totalRevenue: totalRevenue.total,
    };
  },

  // 获取用户详情（增强版）
  getUserDetail: async (userId: number) => {
    const [user] = await executeQuery(`
      SELECT
        u.*,
        COUNT(DISTINCT p.id) as project_count,
        COUNT(DISTINCT ch.id) as message_count,
        COUNT(DISTINCT cp.id) as community_project_count,
        (
          SELECT mo.membership_type
          FROM membership_orders mo
          WHERE mo.user_id = u.id
          AND mo.status = 'paid'
          AND mo.expires_at > NOW()
          ORDER BY mo.expires_at DESC
          LIMIT 1
        ) as membership_type,
        (
          SELECT mo.expires_at
          FROM membership_orders mo
          WHERE mo.user_id = u.id
          AND mo.status = 'paid'
          AND mo.expires_at > NOW()
          ORDER BY mo.expires_at DESC
          LIMIT 1
        ) as membership_expires_at,
        (
          SELECT SUM(mo.discount_price)
          FROM membership_orders mo
          WHERE mo.user_id = u.id
          AND mo.status = 'paid'
        ) as total_spent
      FROM users u
      LEFT JOIN projects p ON u.id = p.user_id
      LEFT JOIN chat_history ch ON u.id = ch.user_id
      LEFT JOIN community_projects cp ON u.id = cp.user_id
      WHERE u.id = ?
      GROUP BY u.id
    `, [userId]) as [unknown];

    if (!user) return null;

    // 获取用户的使用统计
    const [usageStats] = await executeQuery(`
      SELECT
        SUM(requests_count) as total_requests,
        SUM(requests_count * 100) as total_tokens,
        SUM(cost_yuan) as total_cost,
        COUNT(DISTINCT date) as active_days
      FROM usage_stats
      WHERE user_id = ?
      AND date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    `, [userId]) as [any];

    // 获取用户的项目列表
    const projects = await executeQuery(`
      SELECT
        p.id,
        p.title,
        p.is_deployed,
        p.created_at,
        p.updated_at,
        COUNT(DISTINCT pv.id) as version_count,
        COUNT(DISTINCT ch.id) as message_count
      FROM projects p
      LEFT JOIN project_versions pv ON p.id = pv.project_id
      LEFT JOIN chat_history ch ON p.id = ch.project_id
      WHERE p.user_id = ?
      GROUP BY p.id
      ORDER BY p.updated_at DESC
      LIMIT 10
    `, [userId]);

    // 获取用户的会员订单
    const orders = await executeQuery(`
      SELECT
        id,
        order_no,
        membership_type,
        duration_months,
        discount_price,
        status,
        paid_at,
        expires_at,
        created_at
      FROM membership_orders
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 5
    `, [userId]);

    return {
      ...user,
      usage_stats: {
        total_requests: usageStats?.total_requests || 0,
        total_tokens: usageStats?.total_tokens || 0,
        total_cost: usageStats?.total_cost || 0,
        active_days: usageStats?.active_days || 0
      },
      projects,
      orders
    };
  },

  // 更新用户状态
  updateUserStatus: async (userId: number, isActive: boolean) => {
    await executeQuery(`
      UPDATE users
      SET is_active = ?, updated_at = NOW()
      WHERE id = ?
    `, [isActive ? 1 : 0, userId]);
  },

  // 导出用户数据
  exportUsers: async () => {
    const users = await executeQuery(`
      SELECT
        u.id,
        u.nickname,
        u.phone,
        u.created_at,
        u.last_login,
        u.is_active,
        COUNT(DISTINCT p.id) as project_count,
        COUNT(DISTINCT ch.id) as message_count,
        (
          SELECT mo.membership_type
          FROM membership_orders mo
          WHERE mo.user_id = u.id
          AND mo.status = 'paid'
          AND mo.expires_at > NOW()
          ORDER BY mo.expires_at DESC
          LIMIT 1
        ) as membership_type,
        (
          SELECT SUM(mo.discount_price)
          FROM membership_orders mo
          WHERE mo.user_id = u.id
          AND mo.status = 'paid'
        ) as total_spent
      FROM users u
      LEFT JOIN projects p ON u.id = p.user_id
      LEFT JOIN chat_history ch ON u.id = ch.user_id
      GROUP BY u.id
      ORDER BY u.created_at DESC
    `);

    return users;
  },

  // 更新社区项目顺序
  updateCommunityProjectOrder: async (projectId: number, newOrder: number) => {
    try {
      await executeQuery(`
        UPDATE community_projects 
        SET display_order = ? 
        WHERE id = ?
      `, [newOrder, projectId]);
    } catch (error: any) {
      // 如果display_order字段不存在，抛出友好的错误信息
      if (error.code === 'ER_BAD_FIELD_ERROR' && error.sqlMessage?.includes('display_order')) {
        throw new Error('display_order字段不存在，请先运行数据库迁移添加该字段');
      }
      throw error;
    }
  },

  // 获取系统活动日志
  getActivityLogs: async (limit = 100, offset = 0) => {
    // 这里可以根据实际的日志表来查询
    // 目前先返回基于现有数据的活动记录
    const activities = await executeQuery(`
      SELECT 
        'project_created' as type,
        p.id as target_id,
        p.title as target_name,
        p.user_id,
        u.nickname as user_name,
        p.created_at as timestamp
      FROM projects p
      LEFT JOIN users u ON p.user_id = u.id
      UNION ALL
      SELECT 
        'user_registered' as type,
        u.id as target_id,
        u.nickname as target_name,
        u.id as user_id,
        u.nickname as user_name,
        u.created_at as timestamp
      FROM users u
      ORDER BY timestamp DESC
      LIMIT ${limit} OFFSET ${offset}
    `, []);
    
    return activities;
  },

  // ==================== 积分管理相关查询 ====================

  // 获取积分统计信息（优化版）
  getPointsStats: async () => {
    // 使用单个查询获取所有积分统计数据
    const [stats] = await executeQuery(`
      SELECT
        (SELECT COALESCE(SUM(points), 0) FROM users) as total_points,
        (SELECT COALESCE(SUM(total_earned_points), 0) FROM users) as total_earned,
        (SELECT COALESCE(SUM(total_spent_points), 0) FROM users) as total_spent,
        (SELECT COALESCE(SUM(points_expired), 0) FROM points_expiry_log) as total_expired,
        (SELECT COUNT(*) FROM users WHERE points > 0) as active_users,
        (SELECT COUNT(*) FROM points_transactions) as total_transactions,
        (SELECT COALESCE(AVG(points), 0) FROM users WHERE points > 0) as avg_balance,
        (SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()) as new_users_today
    `) as [any];

    return {
      totalPoints: stats.total_points,
      totalEarned: stats.total_earned,
      totalSpent: stats.total_spent,
      totalExpired: stats.total_expired,
      activeUsers: stats.active_users,
      totalTransactions: stats.total_transactions,
      averageBalance: Math.round(stats.avg_balance),
      newUsersToday: stats.new_users_today
    };
  },

  // 获取用户积分信息列表（修复版）
  getUserPointsList: async (limit = 50, offset = 0) => {
    const users = await executeQuery(`
      SELECT
        u.id,
        u.nickname,
        u.phone,
        u.points,
        u.total_earned_points,
        u.total_spent_points,
        u.created_at,
        pt_last.last_transaction_at,
        COALESCE(pb.activity_points, 0) as activity_points,
        COALESCE(pb.subscription_points, 0) as subscription_points,
        COALESCE(pb.recharge_points, 0) as recharge_points
      FROM users u
      LEFT JOIN (
        SELECT
          user_id,
          MAX(created_at) as last_transaction_at
        FROM points_transactions
        GROUP BY user_id
      ) pt_last ON u.id = pt_last.user_id
      LEFT JOIN (
        SELECT
          user_id,
          SUM(CASE WHEN points_type = 'activity' AND is_active = 1 THEN points_amount ELSE 0 END) as activity_points,
          SUM(CASE WHEN points_type = 'subscription' AND is_active = 1 THEN points_amount ELSE 0 END) as subscription_points,
          SUM(CASE WHEN points_type = 'recharge' AND is_active = 1 THEN points_amount ELSE 0 END) as recharge_points
        FROM user_points_balance
        GROUP BY user_id
      ) pb ON u.id = pb.user_id
      ORDER BY u.points DESC
      LIMIT ${limit} OFFSET ${offset}
    `);

    return users.map((user: any) => ({
      ...user,
      points_breakdown: {
        activity: user.activity_points || 0,
        subscription: user.subscription_points || 0,
        recharge: user.recharge_points || 0
      }
    }));
  },

  // 获取积分交易记录（优化版）
  getPointsTransactions: async (options: {
    limit?: number
    offset?: number
    transactionType?: string
    sourceType?: string
    pointsType?: string
    dateRange?: string
    search?: string
    sortBy?: string
    sortOrder?: string
  } = {}) => {
    const {
      limit = 50,
      offset = 0,
      transactionType = 'all',
      sourceType = 'all',
      pointsType = 'all',
      dateRange = '30d',
      search = '',
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = options;

    let whereConditions = ['1=1']; // 基础条件
    let params: any[] = [];

    // 时间范围过滤（优先处理，利用索引）
    if (dateRange !== 'all') {
      const days = parseInt(dateRange.replace('d', ''));
      whereConditions.push('pt.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)');
      params.push(days);
    }

    // 交易类型过滤
    if (transactionType !== 'all') {
      whereConditions.push('pt.transaction_type = ?');
      params.push(transactionType);
    }

    // 来源类型过滤
    if (sourceType !== 'all') {
      whereConditions.push('pt.source_type = ?');
      params.push(sourceType);
    }

    // 积分类型过滤
    if (pointsType !== 'all') {
      whereConditions.push('pt.points_type = ?');
      params.push(pointsType);
    }

    // 搜索条件（最后处理）
    if (search) {
      whereConditions.push(`(
        u.nickname LIKE ? OR
        pt.id = ? OR
        pt.description LIKE ?
      )`);
      params.push(`%${search}%`, search, `%${search}%`);
    }

    const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

    // 排序
    const validSortFields = ['created_at', 'points_amount', 'balance_after'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder === 'asc' ? 'ASC' : 'DESC';

    const transactions = await executeQuery(`
      SELECT
        pt.id,
        pt.user_id,
        COALESCE(u.nickname, CONCAT('用户', pt.user_id)) as user_name,
        pt.transaction_type,
        pt.points_amount,
        pt.balance_before,
        pt.balance_after,
        pt.source_type,
        pt.points_type,
        pt.description,
        pt.created_at
      FROM points_transactions pt
      LEFT JOIN users u ON pt.user_id = u.id
      ${whereClause}
      ORDER BY pt.${sortField} ${order}
      LIMIT ? OFFSET ?
    `, [...params, limit, offset]);

    return transactions;
  },

  // 获取用户积分详情（优化版）
  getUserPointsDetail: async (userId: number) => {
    const [user] = await executeQuery(`
      SELECT
        u.id,
        u.nickname,
        u.phone,
        u.points,
        u.total_earned_points,
        u.total_spent_points,
        u.created_at,
        pb.activity_points,
        pb.subscription_points,
        pb.recharge_points
      FROM users u
      LEFT JOIN (
        SELECT
          user_id,
          SUM(CASE WHEN points_type = 'activity' AND is_active = 1 THEN points_amount ELSE 0 END) as activity_points,
          SUM(CASE WHEN points_type = 'subscription' AND is_active = 1 THEN points_amount ELSE 0 END) as subscription_points,
          SUM(CASE WHEN points_type = 'recharge' AND is_active = 1 THEN points_amount ELSE 0 END) as recharge_points
        FROM user_points_balance
        WHERE user_id = ${userId}
        GROUP BY user_id
      ) pb ON u.id = pb.user_id
      WHERE u.id = ${userId}
    `) as [any];

    if (!user) return null;

    // 获取最近的交易记录
    const recentTransactions = await executeQuery(`
      SELECT
        id,
        transaction_type,
        points_amount,
        balance_before,
        balance_after,
        source_type,
        points_type,
        description,
        created_at
      FROM points_transactions
      WHERE user_id = ${userId}
      ORDER BY created_at DESC
      LIMIT 10
    `);

    // 获取积分余额详情（包含过期时间）
    const pointsBalance = await executeQuery(`
      SELECT
        points_type,
        points_amount,
        expires_at,
        is_active,
        created_at
      FROM user_points_balance
      WHERE user_id = ${userId} AND is_active = 1 AND points_amount > 0
      ORDER BY expires_at ASC, created_at ASC
    `);

    return {
      ...user,
      points_breakdown: {
        activity: user.activity_points || 0,
        subscription: user.subscription_points || 0,
        recharge: user.recharge_points || 0
      },
      recent_transactions: recentTransactions,
      points_balance_details: pointsBalance
    };
  },

  // 调整用户积分（精准版本）
  adjustUserPoints: async (
    userId: number,
    amount: number,
    type: 'add' | 'subtract',
    pointsType: 'activity' | 'subscription' | 'recharge' = 'activity',
    validityDays: number | null = null,
    reason: string = '管理员调整'
  ) => {
    const connection = getPool();
    const conn = await connection.getConnection();

    try {
      await conn.beginTransaction();

      // 获取当前积分余额
      const [currentUser] = await conn.execute(`
        SELECT points, total_earned_points, total_spent_points
        FROM users
        WHERE id = ?
      `, [userId]) as [any];

      if (!currentUser || currentUser.length === 0) {
        throw new Error('用户不存在');
      }

      const currentBalance = currentUser[0].points;
      const currentEarned = currentUser[0].total_earned_points;
      const currentSpent = currentUser[0].total_spent_points;

      // 计算新余额
      const newBalance = type === 'add' ? currentBalance + amount : currentBalance - amount;

      if (newBalance < 0) {
        throw new Error('积分余额不足');
      }

      // 更新用户积分
      const newEarned = type === 'add' ? currentEarned + amount : currentEarned;
      const newSpent = type === 'subtract' ? currentSpent + amount : currentSpent;

      await conn.execute(`
        UPDATE users
        SET points = ?,
            total_earned_points = ?,
            total_spent_points = ?,
            updated_at = NOW()
        WHERE id = ?
      `, [newBalance, newEarned, newSpent, userId]);

      // 如果是增加积分，需要在积分余额表中记录
      if (type === 'add') {
        const expiresAt = validityDays && validityDays < 999999
          ? new Date(Date.now() + validityDays * 24 * 60 * 60 * 1000)
          : null;

        await conn.execute(`
          INSERT INTO user_points_balance (
            user_id,
            points_type,
            points_amount,
            expires_at,
            is_active,
            created_at
          ) VALUES (?, ?, ?, ?, 1, NOW())
        `, [userId, pointsType, amount, expiresAt]);
      } else {
        // 如果是扣减积分，需要从积分余额表中扣减（按消耗顺序：订阅->活动->充值）
        let remainingAmount = amount;

        // 按积分类型优先级扣减：subscription -> activity -> recharge
        const priorityOrder = ['subscription', 'activity', 'recharge'];

        for (const currentType of priorityOrder) {
          if (remainingAmount <= 0) break;

          // 获取该类型的积分余额记录（按过期时间排序，先过期的先扣）
          const [balanceRecords] = await conn.execute(`
            SELECT id, points_amount, expires_at
            FROM user_points_balance
            WHERE user_id = ? AND points_type = ? AND is_active = 1 AND points_amount > 0
            ORDER BY expires_at ASC, created_at ASC
          `, [userId, currentType]) as [any];

          for (const record of balanceRecords) {
            if (remainingAmount <= 0) break;

            const deductAmount = Math.min(remainingAmount, record.points_amount);
            const newRecordAmount = record.points_amount - deductAmount;

            // 记录消耗日志
            await conn.execute(`
              INSERT INTO points_consumption_log (
                user_id,
                transaction_id,
                balance_record_id,
                points_consumed,
                created_at
              ) VALUES (?, LAST_INSERT_ID(), ?, ?, NOW())
            `, [userId, record.id, deductAmount]);

            if (newRecordAmount <= 0) {
              // 完全扣完，标记为非活跃
              await conn.execute(`
                UPDATE user_points_balance
                SET points_amount = 0, is_active = 0, updated_at = NOW()
                WHERE id = ?
              `, [record.id]);
            } else {
              // 部分扣减
              await conn.execute(`
                UPDATE user_points_balance
                SET points_amount = ?, updated_at = NOW()
                WHERE id = ?
              `, [newRecordAmount, record.id]);
            }

            remainingAmount -= deductAmount;
          }
        }
      }

      // 记录交易
      await conn.execute(`
        INSERT INTO points_transactions (
          user_id,
          transaction_type,
          points_amount,
          balance_before,
          balance_after,
          source_type,
          points_type,
          description,
          created_at
        ) VALUES (?, ?, ?, ?, ?, 'admin_adjust', ?, ?, NOW())
      `, [
        userId,
        type === 'add' ? 'earn' : 'spend',
        amount,
        currentBalance,
        newBalance,
        type === 'add' ? pointsType : null,
        reason
      ]);

      await conn.commit();

      return {
        success: true,
        newBalance,
        pointsType: type === 'add' ? pointsType : null,
        validityDays: type === 'add' ? validityDays : null,
        message: `积分${type === 'add' ? '增加' : '扣减'}成功`
      };

    } catch (error) {
      await conn.rollback();
      throw error;
    } finally {
      conn.release();
    }
  },

  // 导出积分数据
  exportPointsData: async (type: 'users' | 'transactions') => {
    if (type === 'users') {
      return await executeQuery(`
        SELECT
          u.id,
          u.nickname,
          u.phone,
          u.points,
          u.total_earned_points,
          u.total_spent_points,
          u.created_at,
          (
            SELECT MAX(pt.created_at)
            FROM points_transactions pt
            WHERE pt.user_id = u.id
          ) as last_transaction_at
        FROM users u
        ORDER BY u.points DESC
      `);
    } else {
      return await executeQuery(`
        SELECT
          pt.id,
          pt.user_id,
          u.nickname as user_name,
          pt.transaction_type,
          pt.points_amount,
          pt.balance_before,
          pt.balance_after,
          pt.source_type,
          pt.points_type,
          pt.description,
          pt.created_at
        FROM points_transactions pt
        LEFT JOIN users u ON pt.user_id = u.id
        ORDER BY pt.created_at DESC
      `);
    }
  },

  // 处理过期积分
  processExpiredPoints: async () => {
    const connection = getPool();
    const conn = await connection.getConnection();

    try {
      await conn.beginTransaction();

      // 查找所有过期的积分记录
      const [expiredRecords] = await conn.execute(`
        SELECT
          upb.id,
          upb.user_id,
          upb.points_type,
          upb.points_amount,
          upb.expires_at,
          u.points as current_points
        FROM user_points_balance upb
        JOIN users u ON upb.user_id = u.id
        WHERE upb.is_active = 1
        AND upb.expires_at IS NOT NULL
        AND upb.expires_at <= NOW()
        AND upb.points_amount > 0
      `) as [any];

      let totalExpiredPoints = 0;
      const expiredUsers = new Map();

      for (const record of expiredRecords) {
        // 标记积分记录为过期
        await conn.execute(`
          UPDATE user_points_balance
          SET is_active = 0, updated_at = NOW()
          WHERE id = ?
        `, [record.id]);

        // 累计用户过期积分
        if (!expiredUsers.has(record.user_id)) {
          expiredUsers.set(record.user_id, {
            userId: record.user_id,
            currentPoints: record.current_points,
            expiredAmount: 0
          });
        }

        const userExpired = expiredUsers.get(record.user_id);
        userExpired.expiredAmount += record.points_amount;
        totalExpiredPoints += record.points_amount;

        // 记录过期日志
        await conn.execute(`
          INSERT INTO points_expiry_log (
            user_id,
            points_type,
            points_expired,
            expired_at,
            created_at
          ) VALUES (?, ?, ?, ?, NOW())
        `, [record.user_id, record.points_type, record.points_amount, record.expires_at]);
      }

      // 更新用户积分余额
      for (const [userId, userData] of expiredUsers) {
        const newBalance = Math.max(0, userData.currentPoints - userData.expiredAmount);

        await conn.execute(`
          UPDATE users
          SET points = ?, updated_at = NOW()
          WHERE id = ?
        `, [newBalance, userId]);

        // 记录积分过期交易
        await conn.execute(`
          INSERT INTO points_transactions (
            user_id,
            transaction_type,
            points_amount,
            balance_before,
            balance_after,
            source_type,
            description,
            created_at
          ) VALUES (?, 'spend', ?, ?, ?, 'system_expire', '积分过期', NOW())
        `, [userId, userData.expiredAmount, userData.currentPoints, newBalance]);
      }

      await conn.commit();

      return {
        success: true,
        expiredRecordsCount: expiredRecords.length,
        totalExpiredPoints,
        affectedUsersCount: expiredUsers.size
      };

    } catch (error) {
      await conn.rollback();
      throw error;
    } finally {
      conn.release();
    }
  },

  // 获取即将过期的积分
  getExpiringPoints: async (days = 7) => {
    const expiringPoints = await executeQuery(`
      SELECT
        upb.id,
        upb.user_id,
        u.nickname,
        u.phone,
        upb.points_type,
        upb.points_amount,
        upb.expires_at,
        DATEDIFF(upb.expires_at, NOW()) as days_until_expiry
      FROM user_points_balance upb
      JOIN users u ON upb.user_id = u.id
      WHERE upb.is_active = 1
      AND upb.expires_at IS NOT NULL
      AND upb.expires_at > NOW()
      AND upb.expires_at <= DATE_ADD(NOW(), INTERVAL ? DAY)
      AND upb.points_amount > 0
      ORDER BY upb.expires_at ASC
    `, [days]);

    return expiringPoints;
  },

  // ==================== 系统配置管理相关查询 ====================

  // 获取系统配置
  getSystemConfigs: async () => {
    const configs = await executeQuery(`
      SELECT
        id,
        setting_key as config_key,
        setting_value as config_value,
        setting_type as config_type,
        description,
        category,
        is_active
      FROM system_settings
      WHERE is_active = 1
      ORDER BY category, setting_key
    `);

    return configs;
  },

  // 更新系统配置
  updateSystemConfigs: async (configs: any[]) => {
    const connection = getPool();
    const conn = await connection.getConnection();

    try {
      await conn.beginTransaction();

      for (const config of configs) {
        await conn.execute(`
          UPDATE system_settings
          SET setting_value = ?, updated_at = NOW()
          WHERE setting_key = ?
        `, [config.config_value, config.config_key]);
      }

      await conn.commit();
    } catch (error) {
      await conn.rollback();
      throw error;
    } finally {
      conn.release();
    }
  },

  // 获取AI模型配置
  getAiModels: async () => {
    const models = await executeQuery(`
      SELECT
        id,
        model_key,
        model_name,
        points_per_request,
        description,
        is_active,
        display_order
      FROM ai_models
      ORDER BY display_order, id
    `);

    return models;
  },

  // 更新AI模型配置
  updateAiModels: async (models: any[]) => {
    const connection = getPool();
    const conn = await connection.getConnection();

    try {
      await conn.beginTransaction();

      for (const model of models) {
        await conn.execute(`
          UPDATE ai_models
          SET
            model_name = ?,
            points_per_request = ?,
            description = ?,
            is_active = ?,
            display_order = ?,
            updated_at = NOW()
          WHERE id = ?
        `, [
          model.model_name,
          model.points_per_request,
          model.description,
          model.is_active ? 1 : 0,
          model.display_order,
          model.id
        ]);
      }

      await conn.commit();
    } catch (error) {
      await conn.rollback();
      throw error;
    } finally {
      conn.release();
    }
  },

  // 获取导出类型配置
  getExportTypes: async () => {
    const types = await executeQuery(`
      SELECT
        id,
        export_key,
        export_name,
        points_cost,
        description,
        is_active,
        display_order
      FROM export_types
      ORDER BY display_order, id
    `);

    return types;
  },

  // 更新导出类型配置
  updateExportTypes: async (types: any[]) => {
    const connection = getPool();
    const conn = await connection.getConnection();

    try {
      await conn.beginTransaction();

      for (const type of types) {
        await conn.execute(`
          UPDATE export_types
          SET
            export_name = ?,
            points_cost = ?,
            description = ?,
            is_active = ?,
            display_order = ?,
            updated_at = NOW()
          WHERE id = ?
        `, [
          type.export_name,
          type.points_cost,
          type.description,
          type.is_active ? 1 : 0,
          type.display_order,
          type.id
        ]);
      }

      await conn.commit();
    } catch (error) {
      await conn.rollback();
      throw error;
    } finally {
      conn.release();
    }
  },

  // 获取订阅计划配置
  getSubscriptionPlans: async () => {
    const plans = await executeQuery(`
      SELECT
        id,
        plan_key,
        plan_type,
        plan_name,
        duration_months,
        original_price,
        discount_price,
        points_included,
        points_validity_days,
        features,
        is_active,
        display_order
      FROM subscription_plans
      ORDER BY display_order, id
    `);

    return plans;
  },

  // 更新订阅计划配置
  updateSubscriptionPlans: async (plans: any[]) => {
    const connection = getPool();
    const conn = await connection.getConnection();

    try {
      await conn.beginTransaction();

      for (const plan of plans) {
        await conn.execute(`
          UPDATE subscription_plans
          SET
            plan_name = ?,
            duration_months = ?,
            original_price = ?,
            discount_price = ?,
            points_included = ?,
            points_validity_days = ?,
            features = ?,
            is_active = ?,
            display_order = ?,
            updated_at = NOW()
          WHERE id = ?
        `, [
          plan.plan_name,
          plan.duration_months,
          plan.original_price,
          plan.discount_price,
          plan.points_included,
          plan.points_validity_days,
          JSON.stringify(plan.features),
          plan.is_active ? 1 : 0,
          plan.display_order,
          plan.id
        ]);
      }

      await conn.commit();
    } catch (error) {
      await conn.rollback();
      throw error;
    } finally {
      conn.release();
    }
  },

  // 获取充值套餐配置
  getPointsPackages: async () => {
    const packages = await executeQuery(`
      SELECT
        id,
        package_key,
        package_name,
        points_amount,
        original_price,
        discount_price,
        bonus_points,
        description,
        is_active,
        display_order
      FROM points_packages
      ORDER BY display_order, id
    `);

    return packages;
  },

  // 更新充值套餐配置
  updatePointsPackages: async (packages: any[]) => {
    const connection = getPool();
    const conn = await connection.getConnection();

    try {
      await conn.beginTransaction();

      for (const pkg of packages) {
        await conn.execute(`
          UPDATE points_packages
          SET
            package_name = ?,
            points_amount = ?,
            original_price = ?,
            discount_price = ?,
            bonus_points = ?,
            description = ?,
            is_active = ?,
            display_order = ?,
            updated_at = NOW()
          WHERE id = ?
        `, [
          pkg.package_name,
          pkg.points_amount,
          pkg.original_price,
          pkg.discount_price,
          pkg.bonus_points,
          pkg.description,
          pkg.is_active ? 1 : 0,
          pkg.display_order,
          pkg.id
        ]);
      }

      await conn.commit();
    } catch (error) {
      await conn.rollback();
      throw error;
    } finally {
      conn.release();
    }
  },
};