"use client"

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { FolderOpen, Search, Eye, ExternalLink, Server, Clock, Filter } from 'lucide-react'
import { formatDate, formatNumber, truncateText } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface Project {
  id: number
  title: string
  user_id: number
  user_name: string
  user_phone: string
  is_deployed: boolean
  deploy_url: string
  created_at: string
  updated_at: string
  version_count: number
  message_count: number
}

export default function ProjectsPage() {
  const searchParams = useSearchParams()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState(searchParams.get('filter') || 'all')

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await fetch('/api/admin/projects')
        if (response.ok) {
          const data = await response.json()
          setProjects(data)
        }
      } catch (error) {
        console.error('Failed to fetch projects:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchProjects()
  }, [])

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.user_name?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterType === 'all' || 
                         (filterType === 'deployed' && project.is_deployed) ||
                         (filterType === 'not_deployed' && !project.is_deployed)
    
    return matchesSearch && matchesFilter
  })

  if (loading) {
    return (
      <div className="flex-1 p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-64 mb-8"></div>
          <div className="admin-card">
            <div className="p-6">
              <div className="h-6 bg-muted rounded w-48 mb-4"></div>
              <div className="space-y-4">
                {[...Array(10)].map((_, i) => (
                  <div key={i} className="h-16 bg-muted rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold flex items-center">
          <FolderOpen className="mr-3 h-8 w-8" />
          项目管理
        </h1>
        <p className="text-muted-foreground mt-2">
          管理和查看所有用户项目
        </p>
      </div>

      <div className="admin-card">
        {/* 搜索和过滤 */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="搜索项目或用户..."
                  className="pl-10 pr-4 py-2 border border-input rounded-md bg-background text-foreground"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="border border-input rounded-md px-3 py-2 bg-background text-foreground"
                >
                  <option value="all">全部项目</option>
                  <option value="deployed">已部署</option>
                  <option value="not_deployed">未部署</option>
                </select>
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>共 {formatNumber(filteredProjects.length)} 个项目</span>
              <span>已部署: {formatNumber(filteredProjects.filter(p => p.is_deployed).length)}</span>
            </div>
          </div>
        </div>

        {/* 项目表格 */}
        <div className="overflow-x-auto">
          <table className="admin-table">
            <thead>
              <tr>
                <th>项目ID</th>
                <th>项目名称</th>
                <th>创建者</th>
                <th>创建时间</th>
                <th>更新时间</th>
                <th>版本数</th>
                <th>对话数</th>
                <th>部署状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {filteredProjects.map((project) => (
                <tr key={project.id}>
                  <td className="font-mono">{project.id}</td>
                  <td className="font-medium max-w-xs">
                    <div className="truncate" title={project.title}>
                      {truncateText(project.title, 30)}
                    </div>
                  </td>
                  <td>
                    <div className="flex flex-col">
                      <span className="font-medium">
                        {project.user_name || `用户${project.user_id}`}
                      </span>
                      <span className="text-xs text-muted-foreground font-mono">
                        ID: {project.user_id}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center text-sm">
                      <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
                      {formatDate(project.created_at)}
                    </div>
                  </td>
                  <td>{formatDate(project.updated_at)}</td>
                  <td>
                    <span className="font-medium text-blue-600">
                      {formatNumber(project.version_count)}
                    </span>
                  </td>
                  <td>
                    <span className="font-medium text-green-600">
                      {formatNumber(project.message_count)}
                    </span>
                  </td>
                  <td>
                    <div className="flex flex-col space-y-1">
                      <span className={`status-badge ${project.is_deployed ? 'deployed' : 'pending'}`}>
                        {project.is_deployed ? '已部署' : '未部署'}
                      </span>
                      {project.is_deployed && project.deploy_url && (
                        <a
                          href={project.deploy_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-blue-600 hover:underline flex items-center"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          查看
                        </a>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          // TODO: 实现项目详情查看
                          console.log('查看项目详情:', project.id)
                        }}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {project.is_deployed && project.deploy_url && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(project.deploy_url, '_blank')}
                        >
                          <Server className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredProjects.length === 0 && (
          <div className="p-12 text-center text-muted-foreground">
            <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">暂无项目</p>
            <p className="text-sm">
              {searchTerm ? '没有找到匹配的项目' : '系统中还没有项目'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
} 