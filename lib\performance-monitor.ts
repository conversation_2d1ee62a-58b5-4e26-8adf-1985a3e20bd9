// 性能监控工具

interface QueryPerformance {
  query: string
  duration: number
  timestamp: Date
  params?: any[]
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private queryLogs: QueryPerformance[] = []
  private slowQueryThreshold = 1000 // 1秒

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // 记录查询性能
  logQuery(query: string, duration: number, params?: any[]) {
    const log: QueryPerformance = {
      query: query.replace(/\s+/g, ' ').trim(),
      duration,
      timestamp: new Date(),
      params
    }

    this.queryLogs.push(log)

    // 只保留最近1000条记录
    if (this.queryLogs.length > 1000) {
      this.queryLogs = this.queryLogs.slice(-1000)
    }

    // 记录慢查询
    if (duration > this.slowQueryThreshold) {
      console.warn(`🐌 慢查询检测 (${duration}ms):`, {
        query: log.query,
        params: log.params,
        duration: `${duration}ms`
      })
    }
  }

  // 获取性能统计
  getPerformanceStats() {
    if (this.queryLogs.length === 0) {
      return {
        totalQueries: 0,
        averageDuration: 0,
        slowQueries: 0,
        fastestQuery: 0,
        slowestQuery: 0
      }
    }

    const durations = this.queryLogs.map(log => log.duration)
    const slowQueries = this.queryLogs.filter(log => log.duration > this.slowQueryThreshold)

    return {
      totalQueries: this.queryLogs.length,
      averageDuration: Math.round(durations.reduce((a, b) => a + b, 0) / durations.length),
      slowQueries: slowQueries.length,
      fastestQuery: Math.min(...durations),
      slowestQuery: Math.max(...durations),
      slowQueryThreshold: this.slowQueryThreshold
    }
  }

  // 获取慢查询列表
  getSlowQueries(limit = 10) {
    return this.queryLogs
      .filter(log => log.duration > this.slowQueryThreshold)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit)
  }

  // 获取查询频率统计
  getQueryFrequency() {
    const frequency: { [key: string]: number } = {}
    
    this.queryLogs.forEach(log => {
      // 提取查询类型（SELECT, INSERT, UPDATE, DELETE）
      const queryType = log.query.split(' ')[0].toUpperCase()
      frequency[queryType] = (frequency[queryType] || 0) + 1
    })

    return frequency
  }

  // 清除日志
  clearLogs() {
    this.queryLogs = []
  }

  // 设置慢查询阈值
  setSlowQueryThreshold(threshold: number) {
    this.slowQueryThreshold = threshold
  }
}

// 查询性能装饰器
export function measureQueryPerformance(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value
  const monitor = PerformanceMonitor.getInstance()

  descriptor.value = async function (...args: any[]) {
    const startTime = Date.now()
    
    try {
      const result = await method.apply(this, args)
      const duration = Date.now() - startTime
      
      // 记录性能
      monitor.logQuery(propertyName, duration, args)
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      monitor.logQuery(`${propertyName} (ERROR)`, duration, args)
      throw error
    }
  }

  return descriptor
}

export default PerformanceMonitor
