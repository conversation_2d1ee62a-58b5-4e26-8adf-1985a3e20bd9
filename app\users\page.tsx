"use client"

import { useEffect, useState, useCallback } from 'react'
import {
  Users,
  Search,
  Eye,
  MoreHorizontal,
  Filter,
  Download,
  UserCheck,
  UserX,
  Crown,
  Zap,
  Calendar,
  Phone,
  Mail,
  Activity,
  TrendingUp,
  DollarSign,
  MessageSquare,
  FolderOpen,
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  BarChart3
} from 'lucide-react'
import { formatDate, formatNumber, formatCurrency } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from '@/components/ui/tabs'

interface User {
  id: number
  phone: string
  nickname: string
  avatar_url?: string
  created_at: string
  updated_at: string
  last_login: string
  is_active: boolean
  project_count: number
  message_count: number
  community_project_count?: number
  membership_type?: 'free' | 'pro' | 'max'
  membership_expires_at?: string
  total_spent?: number
  last_activity?: string
  usage_stats?: {
    total_requests: number
    total_tokens: number
    total_cost: number
    active_days: number
  }
}

interface UserStats {
  totalUsers: number
  activeUsers: number
  newUsersToday: number
  proUsers: number
  maxUsers: number
  totalRevenue: number
}

interface FilterOptions {
  status: 'all' | 'active' | 'inactive'
  membership: 'all' | 'free' | 'pro' | 'max'
  activity: 'all' | 'recent' | 'inactive'
  sortBy: 'created_at' | 'last_login' | 'project_count' | 'message_count' | 'total_spent'
  sortOrder: 'asc' | 'desc'
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showUserDetail, setShowUserDetail] = useState(false)
  const [filters, setFilters] = useState<FilterOptions>({
    status: 'all',
    membership: 'all',
    activity: 'all',
    sortBy: 'created_at',
    sortOrder: 'desc'
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        limit: pageSize.toString(),
        offset: ((currentPage - 1) * pageSize).toString(),
        search: searchTerm,
        status: filters.status,
        membership: filters.membership,
        activity: filters.activity,
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder
      })

      const [usersRes, statsRes] = await Promise.all([
        fetch(`/api/admin/users?${params}`),
        fetch('/api/admin/users/stats')
      ])

      if (usersRes.ok) {
        const userData = await usersRes.json()
        setUsers(userData.users || userData)
      }

      if (statsRes.ok) {
        const statsData = await statsRes.json()
        setStats(statsData)
      }
    } catch (error) {
      console.error('Failed to fetch users:', error)
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, searchTerm, filters])

  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  // 获取用户详情
  const fetchUserDetail = async (userId: number) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`)
      if (response.ok) {
        const userData = await response.json()
        setSelectedUser(userData)
        setShowUserDetail(true)
      }
    } catch (error) {
      console.error('Failed to fetch user detail:', error)
    }
  }

  // 切换用户状态
  const toggleUserStatus = async (userId: number, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: !currentStatus })
      })

      if (response.ok) {
        await fetchUsers()
      }
    } catch (error) {
      console.error('Failed to update user status:', error)
    }
  }

  // 导出用户数据
  const exportUsers = async () => {
    try {
      const response = await fetch('/api/admin/users/export')
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Failed to export users:', error)
    }
  }

  // 过滤和排序用户
  const filteredUsers = users.filter(user => {
    const matchesSearch = !searchTerm ||
      user.nickname?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.phone?.includes(searchTerm) ||
      user.id.toString().includes(searchTerm)

    const matchesStatus = filters.status === 'all' ||
      (filters.status === 'active' && user.is_active) ||
      (filters.status === 'inactive' && !user.is_active)

    const matchesMembership = filters.membership === 'all' ||
      user.membership_type === filters.membership

    const matchesActivity = filters.activity === 'all' ||
      (filters.activity === 'recent' && user.last_login &&
       new Date(user.last_login) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) ||
      (filters.activity === 'inactive' && (!user.last_login ||
       new Date(user.last_login) < new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)))

    return matchesSearch && matchesStatus && matchesMembership && matchesActivity
  })

  // 获取会员类型显示
  const getMembershipBadge = (type?: string) => {
    switch (type) {
      case 'pro':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800"><Crown className="w-3 h-3 mr-1" />Pro</Badge>
      case 'max':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800"><Zap className="w-3 h-3 mr-1" />Max</Badge>
      default:
        return <Badge variant="outline">Free</Badge>
    }
  }

  // 获取活跃状态
  const getActivityStatus = (lastLogin?: string) => {
    if (!lastLogin) return { status: 'never', color: 'text-gray-500', text: '从未登录' }

    const daysSinceLogin = Math.floor((Date.now() - new Date(lastLogin).getTime()) / (1000 * 60 * 60 * 24))

    if (daysSinceLogin === 0) return { status: 'today', color: 'text-green-600', text: '今日活跃' }
    if (daysSinceLogin <= 7) return { status: 'week', color: 'text-blue-600', text: `${daysSinceLogin}天前` }
    if (daysSinceLogin <= 30) return { status: 'month', color: 'text-yellow-600', text: `${daysSinceLogin}天前` }
    return { status: 'inactive', color: 'text-red-600', text: `${daysSinceLogin}天前` }
  }

  if (loading) {
    return (
      <div className="flex-1 p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-64 mb-8"></div>
          {/* 统计卡片骨架 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="admin-card p-6">
                <div className="h-4 bg-muted rounded w-20 mb-4"></div>
                <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                <div className="h-3 bg-muted rounded w-24"></div>
              </div>
            ))}
          </div>
          {/* 表格骨架 */}
          <div className="admin-card">
            <div className="p-6">
              <div className="h-6 bg-muted rounded w-48 mb-4"></div>
              <div className="space-y-4">
                {[...Array(10)].map((_, i) => (
                  <div key={i} className="h-12 bg-muted rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold flex items-center">
          <Users className="mr-3 h-8 w-8" />
          用户管理
        </h1>
        <p className="text-muted-foreground mt-2">
          全面管理用户账户、会员状态和使用情况
        </p>
      </div>

      {/* 统计概览 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.totalUsers)}</p>
              <p className="text-sm text-muted-foreground">总用户数</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/20">
                <UserCheck className="h-5 w-5 text-green-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.activeUsers)}</p>
              <p className="text-sm text-muted-foreground">活跃用户</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/20">
                <TrendingUp className="h-5 w-5 text-purple-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.newUsersToday)}</p>
              <p className="text-sm text-muted-foreground">今日新增</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
                <Crown className="h-5 w-5 text-blue-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.proUsers)}</p>
              <p className="text-sm text-muted-foreground">Pro用户</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/20">
                <Zap className="h-5 w-5 text-purple-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.maxUsers)}</p>
              <p className="text-sm text-muted-foreground">Max用户</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/20">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</p>
              <p className="text-sm text-muted-foreground">总收入</p>
            </div>
          </div>
        </div>
      )}

      <div className="admin-card">
        {/* 搜索和过滤 */}
        <div className="p-6 border-b border-border">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* 搜索框 */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="搜索用户ID、昵称或手机号..."
                className="pl-10 pr-4 py-2 w-full border border-input rounded-md bg-background text-foreground"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* 过滤器 */}
            <div className="flex gap-2">
              <Select value={filters.status} onValueChange={(value: any) => setFilters(prev => ({ ...prev, status: value }))}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">活跃</SelectItem>
                  <SelectItem value="inactive">禁用</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.membership} onValueChange={(value: any) => setFilters(prev => ({ ...prev, membership: value }))}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="会员" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部会员</SelectItem>
                  <SelectItem value="free">Free</SelectItem>
                  <SelectItem value="pro">Pro</SelectItem>
                  <SelectItem value="max">Max</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.activity} onValueChange={(value: any) => setFilters(prev => ({ ...prev, activity: value }))}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="活跃度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="recent">近期活跃</SelectItem>
                  <SelectItem value="inactive">不活跃</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" onClick={exportUsers}>
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              共 {formatNumber(filteredUsers.length)} 个用户
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">排序:</span>
              <Select value={filters.sortBy} onValueChange={(value: any) => setFilters(prev => ({ ...prev, sortBy: value }))}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">注册时间</SelectItem>
                  <SelectItem value="last_login">最后登录</SelectItem>
                  <SelectItem value="project_count">项目数量</SelectItem>
                  <SelectItem value="message_count">对话数量</SelectItem>
                  <SelectItem value="total_spent">消费金额</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters(prev => ({ ...prev, sortOrder: prev.sortOrder === 'asc' ? 'desc' : 'asc' }))}
              >
                {filters.sortOrder === 'asc' ? '↑' : '↓'}
              </Button>
            </div>
          </div>
        </div>

        {/* 用户表格 */}
        <div className="overflow-x-auto">
          <table className="admin-table">
            <thead>
              <tr>
                <th>用户信息</th>
                <th>会员状态</th>
                <th>活跃度</th>
                <th>使用统计</th>
                <th>消费情况</th>
                <th>注册时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => {
                const activity = getActivityStatus(user.last_login)
                return (
                  <tr key={user.id}>
                    {/* 用户信息 */}
                    <td>
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium">
                          {user.nickname ? user.nickname.charAt(0).toUpperCase() : user.id.toString().slice(-1)}
                        </div>
                        <div>
                          <div className="font-medium">
                            {user.nickname || `用户${user.id}`}
                          </div>
                          <div className="text-sm text-muted-foreground font-mono">
                            ID: {user.id} | {user.phone ?
                              `${user.phone.slice(0, 3)}****${user.phone.slice(-4)}` :
                              '未绑定手机'
                            }
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* 会员状态 */}
                    <td>
                      <div className="space-y-1">
                        {getMembershipBadge(user.membership_type)}
                        {user.membership_expires_at && (
                          <div className="text-xs text-muted-foreground">
                            到期: {formatDate(user.membership_expires_at)}
                          </div>
                        )}
                      </div>
                    </td>

                    {/* 活跃度 */}
                    <td>
                      <div className="space-y-1">
                        <div className={`text-sm font-medium ${activity.color}`}>
                          {activity.text}
                        </div>
                        {user.usage_stats && (
                          <div className="text-xs text-muted-foreground">
                            活跃 {user.usage_stats.active_days} 天
                          </div>
                        )}
                      </div>
                    </td>

                    {/* 使用统计 */}
                    <td>
                      <div className="space-y-1">
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="flex items-center">
                            <FolderOpen className="w-3 h-3 mr-1" />
                            {formatNumber(user.project_count)}
                          </span>
                          <span className="flex items-center">
                            <MessageSquare className="w-3 h-3 mr-1" />
                            {formatNumber(user.message_count)}
                          </span>
                        </div>
                        {user.usage_stats && (
                          <div className="text-xs text-muted-foreground">
                            {formatNumber(user.usage_stats.total_requests)} 请求
                          </div>
                        )}
                      </div>
                    </td>

                    {/* 消费情况 */}
                    <td>
                      <div className="space-y-1">
                        <div className="font-medium">
                          {user.total_spent ? formatCurrency(user.total_spent) : '¥0.00'}
                        </div>
                        {user.usage_stats && (
                          <div className="text-xs text-muted-foreground">
                            Token: {formatNumber(user.usage_stats.total_tokens)}
                          </div>
                        )}
                      </div>
                    </td>

                    {/* 注册时间 */}
                    <td>
                      <div className="text-sm">
                        {formatDate(user.created_at)}
                      </div>
                    </td>

                    {/* 状态 */}
                    <td>
                      <Badge variant={user.is_active ? "default" : "secondary"}>
                        {user.is_active ? (
                          <>
                            <CheckCircle className="w-3 h-3 mr-1" />
                            活跃
                          </>
                        ) : (
                          <>
                            <AlertTriangle className="w-3 h-3 mr-1" />
                            禁用
                          </>
                        )}
                      </Badge>
                    </td>

                    {/* 操作 */}
                    <td>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => fetchUserDetail(user.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>用户操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => fetchUserDetail(user.id)}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => toggleUserStatus(user.id, user.is_active)}
                              className={user.is_active ? "text-red-600" : "text-green-600"}
                            >
                              {user.is_active ? (
                                <>
                                  <UserX className="mr-2 h-4 w-4" />
                                  禁用用户
                                </>
                              ) : (
                                <>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  启用用户
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Shield className="mr-2 h-4 w-4" />
                              重置密码
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              发送通知
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>

        {filteredUsers.length === 0 && (
          <div className="p-12 text-center text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">暂无用户</p>
            <p className="text-sm">
              {searchTerm ? '没有找到匹配的用户' : '系统中还没有注册用户'}
            </p>
          </div>
        )}
      </div>

      {/* 用户详情对话框 */}
      <Dialog open={showUserDetail} onOpenChange={setShowUserDetail}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>用户详情</span>
            </DialogTitle>
            <DialogDescription>
              查看用户的详细信息和使用统计
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">基本信息</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-xl font-bold">
                        {selectedUser.nickname ? selectedUser.nickname.charAt(0).toUpperCase() : selectedUser.id.toString().slice(-1)}
                      </div>
                      <div>
                        <div className="font-medium text-lg">
                          {selectedUser.nickname || `用户${selectedUser.id}`}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          ID: {selectedUser.id}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">手机号:</span>
                        <div className="font-mono">
                          {selectedUser.phone || '未绑定'}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">注册时间:</span>
                        <div>{formatDate(selectedUser.created_at)}</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">最后登录:</span>
                        <div>
                          {selectedUser.last_login ?
                            formatDate(selectedUser.last_login) :
                            '从未登录'
                          }
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">账户状态:</span>
                        <div>
                          <Badge variant={selectedUser.is_active ? "default" : "secondary"}>
                            {selectedUser.is_active ? '活跃' : '禁用'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">会员信息</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-muted-foreground">会员类型:</span>
                      <div className="mt-1">
                        {getMembershipBadge(selectedUser.membership_type)}
                      </div>
                    </div>

                    {selectedUser.membership_expires_at && (
                      <div>
                        <span className="text-muted-foreground">到期时间:</span>
                        <div>{formatDate(selectedUser.membership_expires_at)}</div>
                      </div>
                    )}

                    <div>
                      <span className="text-muted-foreground">总消费:</span>
                      <div className="text-lg font-semibold text-green-600">
                        {selectedUser.total_spent ? formatCurrency(selectedUser.total_spent) : '¥0.00'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 使用统计 */}
              <div>
                <h3 className="text-lg font-semibold mb-4">使用统计</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="admin-stats-card">
                    <div className="flex items-center justify-between mb-2">
                      <FolderOpen className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-xl font-bold">{formatNumber(selectedUser.project_count)}</p>
                      <p className="text-sm text-muted-foreground">创建项目</p>
                    </div>
                  </div>

                  <div className="admin-stats-card">
                    <div className="flex items-center justify-between mb-2">
                      <MessageSquare className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-xl font-bold">{formatNumber(selectedUser.message_count)}</p>
                      <p className="text-sm text-muted-foreground">AI对话</p>
                    </div>
                  </div>

                  {selectedUser.usage_stats && (
                    <>
                      <div className="admin-stats-card">
                        <div className="flex items-center justify-between mb-2">
                          <Activity className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                          <p className="text-xl font-bold">{formatNumber(selectedUser.usage_stats.total_requests)}</p>
                          <p className="text-sm text-muted-foreground">API请求</p>
                        </div>
                      </div>

                      <div className="admin-stats-card">
                        <div className="flex items-center justify-between mb-2">
                          <Zap className="h-5 w-5 text-orange-600" />
                        </div>
                        <div>
                          <p className="text-xl font-bold">{formatNumber(selectedUser.usage_stats.total_tokens)}</p>
                          <p className="text-sm text-muted-foreground">Token消耗</p>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}