"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/system-config/page",{

/***/ "(app-pages-browser)/./app/system-config/page.tsx":
/*!************************************!*\
  !*** ./app/system-config/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SystemConfigPage() {\n    var _getActivityPointsConfig_invitation, _getActivityPointsConfig_invitation1;\n    _s();\n    const [configs, setConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiModels, setAiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exportTypes, setExportTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [subscriptionPlans, setSubscriptionPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pointsPackages, setPointsPackages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('points');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SystemConfigPage.useEffect\": ()=>{\n            fetchAllData();\n        }\n    }[\"SystemConfigPage.useEffect\"], []);\n    const fetchAllData = async ()=>{\n        try {\n            setLoading(true);\n            const [configRes, modelsRes, exportRes, plansRes, packagesRes] = await Promise.all([\n                fetch('/api/admin/system-config'),\n                fetch('/api/admin/system-config/ai-models'),\n                fetch('/api/admin/system-config/export-types'),\n                fetch('/api/admin/system-config/subscription-plans'),\n                fetch('/api/admin/system-config/points-packages')\n            ]);\n            if (configRes.ok) {\n                const configData = await configRes.json();\n                setConfigs(configData);\n            }\n            if (modelsRes.ok) {\n                const modelsData = await modelsRes.json();\n                setAiModels(modelsData);\n            }\n            if (exportRes.ok) {\n                const exportData = await exportRes.json();\n                setExportTypes(exportData);\n            }\n            if (plansRes.ok) {\n                const plansData = await plansRes.json();\n                setSubscriptionPlans(plansData);\n            }\n            if (packagesRes.ok) {\n                const packagesData = await packagesRes.json();\n                setPointsPackages(packagesData);\n            }\n        } catch (error) {\n            console.error('Failed to fetch system config:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateConfig = (key, value)=>{\n        setConfigs((prev)=>prev.map((config)=>config.config_key === key ? {\n                    ...config,\n                    config_value: value\n                } : config));\n    };\n    const updateAiModel = (id, field, value)=>{\n        setAiModels((prev)=>prev.map((model)=>model.id === id ? {\n                    ...model,\n                    [field]: value\n                } : model));\n    };\n    const updateExportType = (id, field, value)=>{\n        setExportTypes((prev)=>prev.map((type)=>type.id === id ? {\n                    ...type,\n                    [field]: value\n                } : type));\n    };\n    const updateSubscriptionPlan = (id, field, value)=>{\n        setSubscriptionPlans((prev)=>prev.map((plan)=>plan.id === id ? {\n                    ...plan,\n                    [field]: value\n                } : plan));\n    };\n    const updatePointsPackage = (id, field, value)=>{\n        setPointsPackages((prev)=>prev.map((pkg)=>pkg.id === id ? {\n                    ...pkg,\n                    [field]: value\n                } : pkg));\n    };\n    const saveAllChanges = async ()=>{\n        try {\n            setSaving(true);\n            const savePromises = [\n                fetch('/api/admin/system-config', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        configs\n                    })\n                }),\n                fetch('/api/admin/system-config/ai-models', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        models: aiModels\n                    })\n                }),\n                fetch('/api/admin/system-config/export-types', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        types: exportTypes\n                    })\n                }),\n                fetch('/api/admin/system-config/subscription-plans', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        plans: subscriptionPlans\n                    })\n                }),\n                fetch('/api/admin/system-config/points-packages', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        packages: pointsPackages\n                    })\n                })\n            ];\n            const results = await Promise.all(savePromises);\n            const allSuccess = results.every((res)=>res.ok);\n            if (allSuccess) {\n                console.log('所有配置保存成功');\n            // 可以添加成功提示\n            } else {\n                console.error('部分配置保存失败');\n            // 可以添加错误提示\n            }\n        } catch (error) {\n            console.error('Failed to save configs:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const getConfigValue = function(key) {\n        let defaultValue = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '';\n        const config = configs.find((c)=>c.config_key === key);\n        return config ? config.config_value : defaultValue;\n    };\n    // 解析活动积分配置\n    const getActivityPointsConfig = ()=>{\n        try {\n            const configValue = getConfigValue('activity_points_config', '{}');\n            return JSON.parse(configValue);\n        } catch (error) {\n            console.error('Failed to parse activity_points_config:', error);\n            return {\n                invitation: {\n                    validity_days: 15,\n                    description: '邀请活动积分'\n                },\n                registration: {\n                    validity_days: 30,\n                    description: '注册奖励积分'\n                },\n                special_event: {\n                    validity_days: 7,\n                    description: '特殊活动积分'\n                }\n            };\n        }\n    };\n    // 更新活动积分配置\n    const updateActivityPointsConfig = (activityType, validityDays)=>{\n        const currentConfig = getActivityPointsConfig();\n        const updatedConfig = {\n            ...currentConfig,\n            [activityType]: {\n                ...currentConfig[activityType],\n                validity_days: validityDays\n            }\n        };\n        updateConfig('activity_points_config', JSON.stringify(updatedConfig));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-muted rounded w-64 mb-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(10)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 bg-muted rounded\"\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"mr-3 h-8 w-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"系统参数设置\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mt-2\",\n                                    children: \"管理积分系统、AI模型、导出功能、订阅计划等核心参数\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: fetchAllData,\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"刷新\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: saveAllChanges,\n                                    disabled: saving,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        saving ? '保存中...' : '保存所有更改'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                value: activeTab,\n                onValueChange: setActiveTab,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                        className: \"grid w-full grid-cols-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"points\",\n                                children: \"积分设置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"subscription\",\n                                children: \"订阅控制\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"models\",\n                                children: \"AI模型\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"export\",\n                                children: \"导出功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"plans\",\n                                children: \"订阅计划\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"recharge\",\n                                children: \"充值套餐\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"invitation\",\n                                children: \"邀请设置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"points\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"新用户注册设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"启用注册送积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('new_user_points_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('new_user_points_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"注册送积分数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('new_user_points_amount', '100'),\n                                                            onChange: (e)=>updateConfig('new_user_points_amount', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"积分有效期（天）\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('activity_points_validity_days', '30'),\n                                                            onChange: (e)=>updateConfig('activity_points_validity_days', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"积分消费优先级\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-blue-600 text-white text-xs flex items-center justify-center font-bold\",\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"订阅积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"优先消耗订阅获得的积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"活动积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"订阅积分不足时消耗活动积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-purple-600 text-white text-xs flex items-center justify-center font-bold\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"充值积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"最后消耗充值获得的积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"subscription\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"服务总开关\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"订阅服务\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('subscription_service_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('subscription_service_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"充值服务\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('recharge_service_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('recharge_service_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"显示订阅积分按钮\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('show_subscription_button') === '1',\n                                                            onChange: (e)=>updateConfig('show_subscription_button', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-amber-800 dark:text-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"提示：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            '关闭\"显示订阅积分按钮\"可以隐藏用户菜单中的所有充值入口，适合产品前期免费推广阶段。'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"订阅计划控制\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"免费版计划\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('free_plan_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('free_plan_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Pro版计划\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('pro_plan_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('pro_plan_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Max版计划\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('max_plan_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('max_plan_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"独立控制：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"每个计划可以独立开启/关闭，关闭的计划不会在前端显示对应的订阅卡片。\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Max版积分自定义设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"启用Max版积分调整功能\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('max_plan_custom_points_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('max_plan_custom_points_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"最少积分数量\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: getConfigValue('max_plan_min_points', '2500'),\n                                                                    onChange: (e)=>updateConfig('max_plan_min_points', e.target.value),\n                                                                    className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                    min: \"1000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"最多积分数量\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: getConfigValue('max_plan_max_points', '10000'),\n                                                                    onChange: (e)=>updateConfig('max_plan_max_points', e.target.value),\n                                                                    className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                    min: \"2500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"积分调整步长\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: getConfigValue('max_plan_points_step', '500'),\n                                                                    onChange: (e)=>updateConfig('max_plan_points_step', e.target.value),\n                                                                    className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                    min: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-purple-800 dark:text-purple-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Max版特色：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"用户可以在 \",\n                                                            getConfigValue('max_plan_min_points', '2500'),\n                                                            \" - \",\n                                                            getConfigValue('max_plan_max_points', '10000'),\n                                                            \" 积分范围内，按 \",\n                                                            getConfigValue('max_plan_points_step', '500'),\n                                                            \" 积分步长自定义积分数量，系统会根据积分数量动态计算价格。\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"models\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"AI模型积分消耗设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"模型标识\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"模型名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"每次请求积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"排序\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: aiModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-mono\",\n                                                                children: model.model_key\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: model.model_name,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'model_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: model.points_per_request,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'points_per_request', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: model.description || '',\n                                                                    onChange: (e)=>updateAiModel(model.id, 'description', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    placeholder: \"模型描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: model.is_active,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: model.display_order,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'display_order', parseInt(e.target.value) || 999),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, model.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"export\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"导出功能积分消耗设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"导出类型\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"功能名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"积分消耗\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"排序\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: exportTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-mono\",\n                                                                children: type.export_key\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: type.export_name,\n                                                                    onChange: (e)=>updateExportType(type.id, 'export_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: type.points_cost,\n                                                                    onChange: (e)=>updateExportType(type.id, 'points_cost', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: type.description || '',\n                                                                    onChange: (e)=>updateExportType(type.id, 'description', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    placeholder: \"功能描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: type.is_active,\n                                                                    onChange: (e)=>updateExportType(type.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: type.display_order,\n                                                                    onChange: (e)=>updateExportType(type.id, 'display_order', parseInt(e.target.value) || 999),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, type.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"plans\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"订阅计划设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"计划类型\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"计划名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"时长(月)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"原价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"现价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"包含积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"积分有效期(天)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: subscriptionPlans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: plan.plan_type === 'free' ? 'outline' : plan.plan_type === 'pro' ? 'secondary' : 'default',\n                                                                    children: plan.plan_type.toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 716,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: plan.plan_name,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'plan_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: plan.duration_months,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'duration_months', parseInt(e.target.value) || 1),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: plan.original_price,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'original_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: plan.discount_price,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'discount_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: plan.points_included,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'points_included', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: plan.points_validity_days,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'points_validity_days', parseInt(e.target.value) || 30),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: plan.is_active,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, plan.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"recharge\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"充值套餐设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"套餐标识\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"套餐名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"积分数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"原价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"现价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"赠送积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"排序\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: pointsPackages.map((pkg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-mono\",\n                                                                children: pkg.package_key\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: pkg.package_name,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'package_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: pkg.points_amount,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'points_amount', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: pkg.original_price,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'original_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: pkg.discount_price,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'discount_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: pkg.bonus_points,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'bonus_points', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: pkg.description || '',\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'description', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    placeholder: \"套餐描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: pkg.is_active,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 878,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: pkg.display_order,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'display_order', parseInt(e.target.value) || 999),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, pkg.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 795,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"invitation\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"邀请好友设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"启用邀请奖励\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 914,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('invitation_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('invitation_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 915,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"每次邀请奖励积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('invitation_points_per_user', '100'),\n                                                            onChange: (e)=>updateConfig('invitation_points_per_user', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"邀请积分有效期（天）\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: ((_getActivityPointsConfig_invitation = getActivityPointsConfig().invitation) === null || _getActivityPointsConfig_invitation === void 0 ? void 0 : _getActivityPointsConfig_invitation.validity_days) || 15,\n                                                            onChange: (e)=>updateActivityPointsConfig('invitation', parseInt(e.target.value)),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\",\n                                                            max: \"999\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                            children: \"邀请奖励积分的有效期，过期后积分将自动失效\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 942,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"每个用户最多邀请数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('max_invitations_per_user', '10'),\n                                                            onChange: (e)=>updateConfig('max_invitations_per_user', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"计算说明：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"最多可获得邀请积分 = \",\n                                                            getConfigValue('invitation_points_per_user', '100'),\n                                                            \" \\xd7 \",\n                                                            getConfigValue('max_invitations_per_user', '10'),\n                                                            \" = \",\n                                                            parseInt(getConfigValue('invitation_points_per_user', '100')) * parseInt(getConfigValue('max_invitations_per_user', '10')),\n                                                            \" 积分\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 961,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"有效期：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            ((_getActivityPointsConfig_invitation1 = getActivityPointsConfig().invitation) === null || _getActivityPointsConfig_invitation1 === void 0 ? void 0 : _getActivityPointsConfig_invitation1.validity_days) || 15,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 957,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 912,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"邀请码设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 970,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"邀请码长度\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('invitation_code_length', '8'),\n                                                            onChange: (e)=>updateConfig('invitation_code_length', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"4\",\n                                                            max: \"20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 976,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"邀请码过期天数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('invitation_expire_days', '30'),\n                                                            onChange: (e)=>updateConfig('invitation_expire_days', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 989,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                            children: \"邀请码本身的有效期，与积分有效期不同\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 996,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-green-800 dark:text-green-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"邀请机制：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1002,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"每个用户都有唯一的邀请码，成功邀请新用户注册后获得积分奖励。\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1003,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"区别说明：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1005,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• 邀请码过期天数：邀请链接的有效期\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1007,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• 邀请积分有效期：获得积分的使用期限\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1000,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 968,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 1018,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"活动积分有效期配置\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1017,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: \"为不同类型的活动积分设置独立的有效期\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1021,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 1016,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: Object.entries(getActivityPointsConfig()).map((param)=>{\n                                                        let [activityType, config] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border border-border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium\",\n                                                                            children: config.description || activityType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                            lineNumber: 1030,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            children: activityType\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                            lineNumber: 1033,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 1029,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: \"有效期（天）\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                            lineNumber: 1038,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: config.validity_days || 30,\n                                                                            onChange: (e)=>updateActivityPointsConfig(activityType, parseInt(e.target.value)),\n                                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                            min: \"1\",\n                                                                            max: \"999\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                            lineNumber: 1039,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 1037,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, activityType, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 1028,\n                                                            columnNumber: 21\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1026,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-amber-800 dark:text-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"配置说明：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1054,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"邀请活动积分：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1055,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"通过邀请好友注册获得的积分\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1056,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"注册奖励积分：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"新用户注册时获得的积分\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1058,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"• \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"特殊活动积分：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1059,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"特殊活动或促销获得的积分\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1060,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"注意：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 1061,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"修改有效期只影响新获得的积分，已有积分的有效期不会改变\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 1052,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 1051,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 1015,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 904,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 903,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemConfigPage, \"Nh4uxzK69v1m+lEEzI4/IJ8Yo6M=\");\n_c = SystemConfigPage;\nvar _c;\n$RefreshReg$(_c, \"SystemConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/system-config/page.tsx\n"));

/***/ })

});