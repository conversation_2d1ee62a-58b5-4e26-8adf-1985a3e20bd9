import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET() {
  try {
    const models = await adminQueries.getAiModels()
    return NextResponse.json(models)
  } catch (error) {
    console.error('Failed to fetch AI models:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { models } = await request.json()
    
    if (!Array.isArray(models)) {
      return NextResponse.json(
        { error: 'Invalid models format' },
        { status: 400 }
      )
    }

    await adminQueries.updateAiModels(models)
    
    return NextResponse.json({ 
      success: true, 
      message: 'AI模型配置更新成功' 
    })
  } catch (error) {
    console.error('Failed to update AI models:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
