"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/system-config/page",{

/***/ "(app-pages-browser)/./app/system-config/page.tsx":
/*!************************************!*\
  !*** ./app/system-config/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SystemConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,CreditCard,Download,MessageSquare,RefreshCw,Save,Settings,Sliders,Star,UserPlus,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction SystemConfigPage() {\n    _s();\n    const [configs, setConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [aiModels, setAiModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exportTypes, setExportTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [subscriptionPlans, setSubscriptionPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pointsPackages, setPointsPackages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('points');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SystemConfigPage.useEffect\": ()=>{\n            fetchAllData();\n        }\n    }[\"SystemConfigPage.useEffect\"], []);\n    const fetchAllData = async ()=>{\n        try {\n            setLoading(true);\n            const [configRes, modelsRes, exportRes, plansRes, packagesRes] = await Promise.all([\n                fetch('/api/admin/system-config'),\n                fetch('/api/admin/system-config/ai-models'),\n                fetch('/api/admin/system-config/export-types'),\n                fetch('/api/admin/system-config/subscription-plans'),\n                fetch('/api/admin/system-config/points-packages')\n            ]);\n            if (configRes.ok) {\n                const configData = await configRes.json();\n                setConfigs(configData);\n            }\n            if (modelsRes.ok) {\n                const modelsData = await modelsRes.json();\n                setAiModels(modelsData);\n            }\n            if (exportRes.ok) {\n                const exportData = await exportRes.json();\n                setExportTypes(exportData);\n            }\n            if (plansRes.ok) {\n                const plansData = await plansRes.json();\n                setSubscriptionPlans(plansData);\n            }\n            if (packagesRes.ok) {\n                const packagesData = await packagesRes.json();\n                setPointsPackages(packagesData);\n            }\n        } catch (error) {\n            console.error('Failed to fetch system config:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateConfig = (key, value)=>{\n        setConfigs((prev)=>prev.map((config)=>config.config_key === key ? {\n                    ...config,\n                    config_value: value\n                } : config));\n    };\n    const updateAiModel = (id, field, value)=>{\n        setAiModels((prev)=>prev.map((model)=>model.id === id ? {\n                    ...model,\n                    [field]: value\n                } : model));\n    };\n    const updateExportType = (id, field, value)=>{\n        setExportTypes((prev)=>prev.map((type)=>type.id === id ? {\n                    ...type,\n                    [field]: value\n                } : type));\n    };\n    const updateSubscriptionPlan = (id, field, value)=>{\n        setSubscriptionPlans((prev)=>prev.map((plan)=>plan.id === id ? {\n                    ...plan,\n                    [field]: value\n                } : plan));\n    };\n    const updatePointsPackage = (id, field, value)=>{\n        setPointsPackages((prev)=>prev.map((pkg)=>pkg.id === id ? {\n                    ...pkg,\n                    [field]: value\n                } : pkg));\n    };\n    const saveAllChanges = async ()=>{\n        try {\n            setSaving(true);\n            const savePromises = [\n                fetch('/api/admin/system-config', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        configs\n                    })\n                }),\n                fetch('/api/admin/system-config/ai-models', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        models: aiModels\n                    })\n                }),\n                fetch('/api/admin/system-config/export-types', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        types: exportTypes\n                    })\n                }),\n                fetch('/api/admin/system-config/subscription-plans', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        plans: subscriptionPlans\n                    })\n                }),\n                fetch('/api/admin/system-config/points-packages', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        packages: pointsPackages\n                    })\n                })\n            ];\n            const results = await Promise.all(savePromises);\n            const allSuccess = results.every((res)=>res.ok);\n            if (allSuccess) {\n                console.log('所有配置保存成功');\n            // 可以添加成功提示\n            } else {\n                console.error('部分配置保存失败');\n            // 可以添加错误提示\n            }\n        } catch (error) {\n            console.error('Failed to save configs:', error);\n        } finally{\n            setSaving(false);\n        }\n    };\n    const getConfigValue = function(key) {\n        let defaultValue = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '';\n        const config = configs.find((c)=>c.config_key === key);\n        return config ? config.config_value : defaultValue;\n    };\n    // 解析活动积分配置\n    const getActivityPointsConfig = ()=>{\n        try {\n            const configValue = getConfigValue('activity_points_config', '{}');\n            return JSON.parse(configValue);\n        } catch (error) {\n            console.error('Failed to parse activity_points_config:', error);\n            return {\n                invitation: {\n                    validity_days: 15,\n                    description: '邀请活动积分'\n                },\n                registration: {\n                    validity_days: 30,\n                    description: '注册奖励积分'\n                },\n                special_event: {\n                    validity_days: 7,\n                    description: '特殊活动积分'\n                }\n            };\n        }\n    };\n    // 更新活动积分配置\n    const updateActivityPointsConfig = (activityType, validityDays)=>{\n        const currentConfig = getActivityPointsConfig();\n        const updatedConfig = {\n            ...currentConfig,\n            [activityType]: {\n                ...currentConfig[activityType],\n                validity_days: validityDays\n            }\n        };\n        updateConfig('activity_points_config', JSON.stringify(updatedConfig));\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-muted rounded w-64 mb-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(10)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 bg-muted rounded\"\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"mr-3 h-8 w-8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"系统参数设置\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mt-2\",\n                                    children: \"管理积分系统、AI模型、导出功能、订阅计划等核心参数\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: fetchAllData,\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"刷新\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: saveAllChanges,\n                                    disabled: saving,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        saving ? '保存中...' : '保存所有更改'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                value: activeTab,\n                onValueChange: setActiveTab,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                        className: \"grid w-full grid-cols-7\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"points\",\n                                children: \"积分设置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"subscription\",\n                                children: \"订阅控制\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"models\",\n                                children: \"AI模型\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"export\",\n                                children: \"导出功能\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"plans\",\n                                children: \"订阅计划\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"recharge\",\n                                children: \"充值套餐\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                value: \"invitation\",\n                                children: \"邀请设置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"points\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"新用户注册设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"启用注册送积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('new_user_points_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('new_user_points_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"注册送积分数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('new_user_points_amount', '100'),\n                                                            onChange: (e)=>updateConfig('new_user_points_amount', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"积分有效期（天）\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('activity_points_validity_days', '30'),\n                                                            onChange: (e)=>updateConfig('activity_points_validity_days', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"积分消费优先级\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-blue-600 text-white text-xs flex items-center justify-center font-bold\",\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"订阅积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"优先消耗订阅获得的积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"活动积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"订阅积分不足时消耗活动积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-6 h-6 rounded-full bg-purple-600 text-white text-xs flex items-center justify-center font-bold\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"充值积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"最后消耗充值获得的积分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"subscription\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"服务总开关\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"订阅服务\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('subscription_service_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('subscription_service_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"充值服务\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('recharge_service_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('recharge_service_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"显示订阅积分按钮\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('show_subscription_button') === '1',\n                                                            onChange: (e)=>updateConfig('show_subscription_button', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-amber-800 dark:text-amber-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"提示：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            '关闭\"显示订阅积分按钮\"可以隐藏用户菜单中的所有充值入口，适合产品前期免费推广阶段。'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"订阅计划控制\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"免费版计划\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('free_plan_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('free_plan_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Pro版计划\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('pro_plan_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('pro_plan_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Max版计划\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('max_plan_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('max_plan_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"独立控制：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"每个计划可以独立开启/关闭，关闭的计划不会在前端显示对应的订阅卡片。\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card lg:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Max版积分自定义设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"启用Max版积分调整功能\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('max_plan_custom_points_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('max_plan_custom_points_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"最少积分数量\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: getConfigValue('max_plan_min_points', '2500'),\n                                                                    onChange: (e)=>updateConfig('max_plan_min_points', e.target.value),\n                                                                    className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                    min: \"1000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"最多积分数量\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: getConfigValue('max_plan_max_points', '10000'),\n                                                                    onChange: (e)=>updateConfig('max_plan_max_points', e.target.value),\n                                                                    className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                    min: \"2500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"积分调整步长\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: getConfigValue('max_plan_points_step', '500'),\n                                                                    onChange: (e)=>updateConfig('max_plan_points_step', e.target.value),\n                                                                    className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                                    min: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-purple-800 dark:text-purple-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Max版特色：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"用户可以在 \",\n                                                            getConfigValue('max_plan_min_points', '2500'),\n                                                            \" - \",\n                                                            getConfigValue('max_plan_max_points', '10000'),\n                                                            \" 积分范围内，按 \",\n                                                            getConfigValue('max_plan_points_step', '500'),\n                                                            \" 积分步长自定义积分数量，系统会根据积分数量动态计算价格。\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"models\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"AI模型积分消耗设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"模型标识\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"模型名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"每次请求积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"排序\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: aiModels.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-mono\",\n                                                                children: model.model_key\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: model.model_name,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'model_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: model.points_per_request,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'points_per_request', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: model.description || '',\n                                                                    onChange: (e)=>updateAiModel(model.id, 'description', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    placeholder: \"模型描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: model.is_active,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: model.display_order,\n                                                                    onChange: (e)=>updateAiModel(model.id, 'display_order', parseInt(e.target.value) || 999),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, model.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"export\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"导出功能积分消耗设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"导出类型\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"功能名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"积分消耗\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"排序\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: exportTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-mono\",\n                                                                children: type.export_key\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: type.export_name,\n                                                                    onChange: (e)=>updateExportType(type.id, 'export_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 638,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: type.points_cost,\n                                                                    onChange: (e)=>updateExportType(type.id, 'points_cost', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: type.description || '',\n                                                                    onChange: (e)=>updateExportType(type.id, 'description', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    placeholder: \"功能描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: type.is_active,\n                                                                    onChange: (e)=>updateExportType(type.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 664,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: type.display_order,\n                                                                    onChange: (e)=>updateExportType(type.id, 'display_order', parseInt(e.target.value) || 999),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, type.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"plans\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"订阅计划设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"计划类型\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"计划名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 703,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"时长(月)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"原价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"现价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"包含积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"积分有效期(天)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: subscriptionPlans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: plan.plan_type === 'free' ? 'outline' : plan.plan_type === 'pro' ? 'secondary' : 'default',\n                                                                    children: plan.plan_type.toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 716,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: plan.plan_name,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'plan_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: plan.duration_months,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'duration_months', parseInt(e.target.value) || 1),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: plan.original_price,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'original_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: plan.discount_price,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'discount_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: plan.points_included,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'points_included', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: plan.points_validity_days,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'points_validity_days', parseInt(e.target.value) || 30),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: plan.is_active,\n                                                                    onChange: (e)=>updateSubscriptionPlan(plan.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, plan.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"recharge\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"admin-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"充值套餐设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"admin-table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"套餐标识\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"套餐名称\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"积分数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"原价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"现价\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"赠送积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            children: \"排序\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: pointsPackages.map((pkg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"font-mono\",\n                                                                children: pkg.package_key\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 821,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: pkg.package_name,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'package_name', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: pkg.points_amount,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'points_amount', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: pkg.original_price,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'original_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 840,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: pkg.discount_price,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'discount_price', parseFloat(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: pkg.bonus_points,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'bonus_points', parseInt(e.target.value) || 0),\n                                                                    className: \"w-20 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: pkg.description || '',\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'description', e.target.value),\n                                                                    className: \"w-full px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    placeholder: \"套餐描述\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: pkg.is_active,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'is_active', e.target.checked),\n                                                                    className: \"rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 878,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: pkg.display_order,\n                                                                    onChange: (e)=>updatePointsPackage(pkg.id, 'display_order', parseInt(e.target.value) || 999),\n                                                                    className: \"w-16 px-2 py-1 border border-input rounded bg-background text-foreground\",\n                                                                    min: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, pkg.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 795,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                        value: \"invitation\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"邀请好友设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 907,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"启用邀请奖励\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 914,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: getConfigValue('invitation_enabled') === '1',\n                                                            onChange: (e)=>updateConfig('invitation_enabled', e.target.checked ? '1' : '0'),\n                                                            className: \"rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 915,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"每次邀请奖励积分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('invitation_points_per_user', '100'),\n                                                            onChange: (e)=>updateConfig('invitation_points_per_user', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"每个用户最多邀请数量\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('max_invitations_per_user', '10'),\n                                                            onChange: (e)=>updateConfig('max_invitations_per_user', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"计算说明：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"最多可获得邀请积分 = \",\n                                                            getConfigValue('invitation_points_per_user', '100'),\n                                                            \" \\xd7 \",\n                                                            getConfigValue('max_invitations_per_user', '10'),\n                                                            \" = \",\n                                                            parseInt(getConfigValue('invitation_points_per_user', '100')) * parseInt(getConfigValue('max_invitations_per_user', '10')),\n                                                            \" 积分\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 942,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 912,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"admin-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-border\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_CreditCard_Download_MessageSquare_RefreshCw_Save_Settings_Sliders_Star_UserPlus_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 955,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"邀请码设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"邀请码长度\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('invitation_code_length', '8'),\n                                                            onChange: (e)=>updateConfig('invitation_code_length', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"4\",\n                                                            max: \"20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 962,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"邀请码过期天数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            value: getConfigValue('invitation_expire_days', '30'),\n                                                            onChange: (e)=>updateConfig('invitation_expire_days', e.target.value),\n                                                            className: \"w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground\",\n                                                            min: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                            lineNumber: 973,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 971,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-green-800 dark:text-green-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"邀请机制：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"每个用户都有唯一的邀请码，成功邀请新用户注册后获得积分奖励。\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                                    lineNumber: 981,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                            lineNumber: 904,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                        lineNumber: 903,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\333\\\\LoomRun_admin\\\\app\\\\system-config\\\\page.tsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, this);\n}\n_s(SystemConfigPage, \"Nh4uxzK69v1m+lEEzI4/IJ8Yo6M=\");\n_c = SystemConfigPage;\nvar _c;\n$RefreshReg$(_c, \"SystemConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/system-config/page.tsx\n"));

/***/ })

});