"use client"

import { useEffect, useState, useCallback } from 'react'
import { 
  Globe, 
  Search, 
  Eye, 
  ExternalLink, 
  GripVertical, 
  RefreshCw,
  Loader2,
  CheckCircle,
  AlertCircle,
  Settings,
  Users,
  Calendar,
  Hash,
  TrendingUp,
  Database
} from 'lucide-react'
import { formatDate, formatNumber, truncateText } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface CommunityProject {
  id: number
  title: string
  user_id: number
  user_name: string
  original_project_id: number
  original_title: string
  created_at: string
  updated_at: string
  display_order: number
}

interface DragState {
  draggedItem: CommunityProject | null
  draggedIndex: number | null
  dragOverIndex: number | null
  isDragging: boolean
}

export default function CommunityPage() {
  const [projects, setProjects] = useState<CommunityProject[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [updating, setUpdating] = useState<number | null>(null)
  const [dragState, setDragState] = useState<DragState>({
    draggedItem: null,
    draggedIndex: null,
    dragOverIndex: null,
    isDragging: false
  })
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info'
    message: string
  } | null>(null)

  // 获取项目数据
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/community')
      if (response.ok) {
        const data = await response.json()
        setProjects(data)
      } else {
        throw new Error('获取数据失败')
      }
    } catch (error) {
      console.error('Failed to fetch community projects:', error)
      showNotification('error', '获取社区项目失败')
    } finally {
      setLoading(false)
    }
  }, [])

  // 显示通知
  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message })
    setTimeout(() => setNotification(null), 3000)
  }

  // 运行数据库迁移
  const runMigration = async () => {
    try {
      const response = await fetch('/api/admin/migrate-display-order', {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('迁移失败')
      }

      const result = await response.json()
      showNotification('success', result.message)
      
      // 重新获取数据
      await fetchProjects()
      
    } catch (error) {
      console.error('数据库迁移失败:', error)
      showNotification('error', '数据库迁移失败')
    }
  }

  // 更新项目顺序
  const updateProjectOrder = async (projectId: number, newOrder: number) => {
    try {
      setUpdating(projectId)
      const response = await fetch('/api/admin/community', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          newOrder
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        if (errorData.error?.includes('display_order字段不存在')) {
          const shouldMigrate = confirm('display_order字段不存在，是否运行数据库迁移？')
          if (shouldMigrate) {
            await runMigration()
          }
          return
        }
        throw new Error(errorData.error || '更新失败')
      }

      showNotification('success', '顺序更新成功')
      await fetchProjects()
      
    } catch (error) {
      console.error('更新项目顺序失败:', error)
      showNotification('error', '更新顺序失败')
    } finally {
      setUpdating(null)
    }
  }

  // 拖拽开始
  const handleDragStart = (e: React.DragEvent, project: CommunityProject, index: number) => {
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/html', e.currentTarget.outerHTML)
    
    setDragState({
      draggedItem: project,
      draggedIndex: index,
      dragOverIndex: null,
      isDragging: true
    })
  }

  // 拖拽经过
  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    
    if (dragState.draggedIndex !== index) {
      setDragState(prev => ({
        ...prev,
        dragOverIndex: index
      }))
    }
  }

  // 拖拽离开
  const handleDragLeave = () => {
    setDragState(prev => ({
      ...prev,
      dragOverIndex: null
    }))
  }

  // 拖拽结束
  const handleDragEnd = () => {
    setDragState({
      draggedItem: null,
      draggedIndex: null,
      dragOverIndex: null,
      isDragging: false
    })
  }

  // 拖拽放置
  const handleDrop = async (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault()
    
    const { draggedItem, draggedIndex } = dragState
    
    if (!draggedItem || draggedIndex === null || draggedIndex === targetIndex) {
      handleDragEnd()
      return
    }

    // 乐观更新UI
    const newProjects = [...filteredProjects]
    const [movedItem] = newProjects.splice(draggedIndex, 1)
    newProjects.splice(targetIndex, 0, movedItem)
    
    // 更新display_order
    const updatedProjects = newProjects.map((project, index) => ({
      ...project,
      display_order: index + 1
    }))
    
    // 临时更新UI
    setProjects(prev => {
      const allProjects = [...prev]
      updatedProjects.forEach(updatedProject => {
        const index = allProjects.findIndex(p => p.id === updatedProject.id)
        if (index !== -1) {
          allProjects[index] = updatedProject
        }
      })
      return allProjects
    })

    handleDragEnd()

    // 发送更新请求
    try {
      await updateProjectOrder(draggedItem.id, targetIndex + 1)
    } catch (error) {
      // 如果更新失败，恢复原始数据
      await fetchProjects()
    }
  }

  // 批量重新排序
  const handleBatchReorder = async () => {
    if (!confirm('确定要重新排序所有项目吗？这将按照创建时间重新排列。')) {
      return
    }

    try {
      setLoading(true)
      
      // 按创建时间排序
      const sortedProjects = [...projects].sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
      
      // 批量更新顺序
      for (let i = 0; i < sortedProjects.length; i++) {
        await updateProjectOrder(sortedProjects[i].id, i + 1)
      }
      
      showNotification('success', '批量重新排序完成')
      await fetchProjects()
      
    } catch (error) {
      showNotification('error', '批量重新排序失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  // 过滤项目
  const filteredProjects = projects.filter(project => 
    project.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.user_name?.toLowerCase().includes(searchTerm.toLowerCase())
  ).sort((a, b) => a.display_order - b.display_order)

  // 统计数据
  const stats = {
    totalProjects: projects.length,
    totalUsers: new Set(projects.map(p => p.user_id)).size,
    recentProjects: projects.filter(p => 
      new Date(p.created_at).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000
    ).length
  }

  if (loading) {
    return (
      <div className="flex-1 p-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-lg">加载中...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-8">
      {/* 通知栏 */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center space-x-2 ${
          notification.type === 'success' ? 'bg-green-500 text-white' :
          notification.type === 'error' ? 'bg-red-500 text-white' :
          'bg-blue-500 text-white'
        }`}>
          {notification.type === 'success' && <CheckCircle className="h-5 w-5" />}
          {notification.type === 'error' && <AlertCircle className="h-5 w-5" />}
          <span>{notification.message}</span>
        </div>
      )}

      {/* 头部 */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              <Globe className="mr-3 h-8 w-8" />
              社区管理
            </h1>
            <p className="text-muted-foreground mt-2">
              管理社区项目展示顺序和内容
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              onClick={handleBatchReorder}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <TrendingUp className="h-4 w-4" />
              <span>批量重排</span>
            </Button>
            <Button
              onClick={runMigration}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <Database className="h-4 w-4" />
              <span>数据库迁移</span>
            </Button>
            <Button
              onClick={fetchProjects}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>刷新</span>
            </Button>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
                <Globe className="h-5 w-5 text-blue-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.totalProjects)}</p>
              <p className="text-sm text-muted-foreground">总项目数</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/20">
                <Users className="h-5 w-5 text-green-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.totalUsers)}</p>
              <p className="text-sm text-muted-foreground">贡献用户</p>
            </div>
          </div>

          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/20">
                <Calendar className="h-5 w-5 text-orange-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.recentProjects)}</p>
              <p className="text-sm text-muted-foreground">本周新增</p>
            </div>
          </div>
        </div>

        {/* 搜索栏 */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="搜索项目标题或用户名..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-input rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-ring"
          />
        </div>
      </div>

      {/* 项目列表 */}
      <div className="admin-card">
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">社区项目列表</h3>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Hash className="h-4 w-4" />
              <span>{filteredProjects.length} 个项目</span>
            </div>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            拖拽项目卡片可以调整显示顺序
          </p>
        </div>

        <div className="p-6">
          {filteredProjects.length === 0 ? (
            <div className="text-center py-12">
              <Globe className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <p className="text-lg font-medium text-muted-foreground mb-2">
                {searchTerm ? '没有找到匹配的项目' : '暂无社区项目'}
              </p>
              <p className="text-sm text-muted-foreground">
                {searchTerm ? '尝试修改搜索条件' : '等待用户分享项目到社区'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredProjects.map((project, index) => (
                <div
                  key={project.id}
                  draggable
                  onDragStart={(e) => handleDragStart(e, project, index)}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, index)}
                  onDragEnd={handleDragEnd}
                  className={`
                    group relative p-4 rounded-lg border transition-all duration-200 cursor-move
                    ${dragState.isDragging && dragState.draggedIndex === index 
                      ? 'opacity-50 scale-95 border-primary' 
                      : 'hover:border-primary/50 hover:shadow-md'
                    }
                    ${dragState.dragOverIndex === index && dragState.draggedIndex !== index
                      ? 'border-primary border-2 bg-primary/5'
                      : 'border-border bg-card'
                    }
                    ${updating === project.id ? 'animate-pulse' : ''}
                  `}
                >
                  {/* 拖拽指示器 */}
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <GripVertical className="h-5 w-5 text-muted-foreground" />
                  </div>

                  {/* 顺序指示器 */}
                  <div className="absolute right-2 top-2 px-2 py-1 bg-primary/10 text-primary text-xs rounded-full font-mono">
                    #{project.display_order}
                  </div>

                  <div className="pl-8 pr-16">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-semibold text-lg mb-1 line-clamp-1">
                          {project.title}
                        </h4>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span className="flex items-center">
                            <Users className="h-3 w-3 mr-1" />
                            {project.user_name || '匿名用户'}
                          </span>
                          <span className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {formatDate(project.created_at)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        <span>原项目ID: {project.original_project_id}</span>
                        {project.original_title && (
                          <span className="ml-2">({truncateText(project.original_title, 30)})</span>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          title="预览项目"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          title="打开项目"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* 加载指示器 */}
                  {updating === project.id && (
                    <div className="absolute inset-0 bg-background/50 flex items-center justify-center rounded-lg">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-8 p-4 bg-muted/50 rounded-lg">
        <h4 className="font-medium mb-2 flex items-center">
          <Settings className="h-4 w-4 mr-2" />
          使用说明
        </h4>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• 拖拽项目卡片可以调整在社区页面的显示顺序</li>
          <li>• 顺序数字越小，在社区页面显示越靠前</li>
          <li>• 使用"批量重排"按钮可以按创建时间重新排列所有项目</li>
          <li>• 如果遇到数据库错误，请点击"数据库迁移"按钮</li>
        </ul>
      </div>
    </div>
  )
}