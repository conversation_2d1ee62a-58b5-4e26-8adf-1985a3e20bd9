import { NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET() {
  try {
    const users = await adminQueries.exportUsers()
    
    // 生成CSV内容
    const headers = [
      '用户ID',
      '昵称',
      '手机号',
      '注册时间',
      '最后登录',
      '账户状态',
      '项目数量',
      '对话数量',
      '会员类型',
      '总消费'
    ]
    
    const csvContent = [
      headers.join(','),
      ...users.map((user: any) => [
        user.id,
        `"${user.nickname || ''}"`,
        `"${user.phone || ''}"`,
        `"${new Date(user.created_at).toLocaleString('zh-CN')}"`,
        `"${user.last_login ? new Date(user.last_login).toLocaleString('zh-CN') : '从未登录'}"`,
        user.is_active ? '活跃' : '禁用',
        user.project_count,
        user.message_count,
        user.membership_type || 'free',
        user.total_spent || 0
      ].join(','))
    ].join('\n')
    
    // 添加BOM以支持中文
    const bom = '\uFEFF'
    const csvWithBom = bom + csvContent
    
    return new NextResponse(csvWithBom, {
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="users_export_${new Date().toISOString().split('T')[0]}.csv"`
      }
    })
  } catch (error) {
    console.error('Failed to export users:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
