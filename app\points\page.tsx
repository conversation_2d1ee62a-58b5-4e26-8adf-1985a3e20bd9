"use client"

import { useEffect, useState, useCallback } from 'react'
import {
  Coins,
  Search,
  Eye,
  MoreHorizontal,
  Download,
  Plus,
  Minus,
  TrendingUp,
  TrendingDown,
  Users,
  Activity,
  DollarSign,
  Star,
  Zap,
  Calendar,
  Gift
} from 'lucide-react'
import { formatDate, formatNumber, formatCurrency } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'

interface PointsStats {
  totalPoints: number
  totalEarned: number
  totalSpent: number
  totalExpired: number
  activeUsers: number
  totalTransactions: number
  averageBalance: number
  topEarners: number
}

interface UserPointsInfo {
  id: number
  nickname: string
  phone: string
  points: number
  total_earned_points: number
  total_spent_points: number
  created_at: string
  last_transaction_at?: string
  points_breakdown: {
    activity: number
    subscription: number
    recharge: number
  }
}

interface PointsTransaction {
  id: number
  user_id: number
  user_name: string
  transaction_type: 'earn' | 'spend'
  points_amount: number
  balance_before: number
  balance_after: number
  source_type: string
  points_type?: string
  description?: string
  created_at: string
}

interface FilterOptions {
  transactionType: 'all' | 'earn' | 'spend'
  sourceType: 'all' | 'registration' | 'ai_request' | 'export' | 'recharge' | 'subscription' | 'admin_adjust'
  pointsType: 'all' | 'activity' | 'subscription' | 'recharge'
  dateRange: '7d' | '30d' | '90d' | 'all'
  sortBy: 'created_at' | 'points_amount' | 'balance_after'
  sortOrder: 'asc' | 'desc'
}

export default function PointsPage() {
  const [stats, setStats] = useState<PointsStats | null>(null)
  const [userPoints, setUserPoints] = useState<UserPointsInfo[]>([])
  const [transactions, setTransactions] = useState<PointsTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedUser, setSelectedUser] = useState<UserPointsInfo | null>(null)
  const [showUserDetail, setShowUserDetail] = useState(false)
  const [showAdjustDialog, setShowAdjustDialog] = useState(false)
  const [adjustAmount, setAdjustAmount] = useState('')
  const [adjustReason, setAdjustReason] = useState('')
  const [adjustType, setAdjustType] = useState<'add' | 'subtract'>('add')
  const [adjustPointsType, setAdjustPointsType] = useState<'activity' | 'subscription' | 'recharge'>('activity')
  const [adjustValidityDays, setAdjustValidityDays] = useState('30')
  const [adjusting, setAdjusting] = useState(false)
  const [expiringPoints, setExpiringPoints] = useState<any[]>([])
  const [showExpiringDialog, setShowExpiringDialog] = useState(false)
  const [processingExpired, setProcessingExpired] = useState(false)
  const [filters, setFilters] = useState<FilterOptions>({
    transactionType: 'all',
    sourceType: 'all',
    pointsType: 'all',
    dateRange: '30d',
    sortBy: 'created_at',
    sortOrder: 'desc'
  })

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      const [statsRes, usersRes, transactionsRes] = await Promise.all([
        fetch('/api/admin/points/stats'),
        fetch('/api/admin/points/users'),
        fetch(`/api/admin/points/transactions?${new URLSearchParams({
          transactionType: filters.transactionType,
          sourceType: filters.sourceType,
          pointsType: filters.pointsType,
          dateRange: filters.dateRange,
          sortBy: filters.sortBy,
          sortOrder: filters.sortOrder,
          search: searchTerm
        })}`)
      ])
      
      if (statsRes.ok) {
        const statsData = await statsRes.json()
        setStats(statsData)
      }
      
      if (usersRes.ok) {
        const usersData = await usersRes.json()
        setUserPoints(usersData)
      }
      
      if (transactionsRes.ok) {
        const transactionsData = await transactionsRes.json()
        setTransactions(transactionsData)
      }
    } catch (error) {
      console.error('Failed to fetch points data:', error)
    } finally {
      setLoading(false)
    }
  }, [filters, searchTerm])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  // 获取用户详情
  const fetchUserDetail = async (userId: number) => {
    try {
      const response = await fetch(`/api/admin/points/users/${userId}`)
      if (response.ok) {
        const userData = await response.json()
        setSelectedUser(userData)
        setShowUserDetail(true)
      }
    } catch (error) {
      console.error('Failed to fetch user points detail:', error)
    }
  }

  // 调整用户积分
  const adjustUserPoints = async () => {
    if (!selectedUser || !adjustAmount) return

    try {
      setAdjusting(true)
      const response = await fetch(`/api/admin/points/users/${selectedUser.id}/adjust`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: parseInt(adjustAmount),
          type: adjustType,
          pointsType: adjustPointsType,
          validityDays: adjustType === 'add' ? parseInt(adjustValidityDays) : null,
          reason: adjustReason || '管理员调整'
        })
      })

      const result = await response.json()

      if (response.ok) {
        setShowAdjustDialog(false)
        setAdjustAmount('')
        setAdjustReason('')
        setAdjustPointsType('activity')
        setAdjustValidityDays('30')
        await fetchData()
        if (selectedUser) {
          await fetchUserDetail(selectedUser.id)
        }
        // 可以添加成功提示
        console.log('积分调整成功:', result.message)
      } else {
        console.error('积分调整失败:', result.error)
        // 可以添加错误提示
      }
    } catch (error) {
      console.error('Failed to adjust user points:', error)
    } finally {
      setAdjusting(false)
    }
  }

  // 获取即将过期的积分
  const fetchExpiringPoints = async () => {
    try {
      const response = await fetch('/api/admin/points/expire?days=7')
      if (response.ok) {
        const data = await response.json()
        setExpiringPoints(data)
        setShowExpiringDialog(true)
      }
    } catch (error) {
      console.error('Failed to fetch expiring points:', error)
    }
  }

  // 处理过期积分
  const processExpiredPoints = async () => {
    try {
      setProcessingExpired(true)
      const response = await fetch('/api/admin/points/expire', {
        method: 'POST'
      })

      const result = await response.json()

      if (response.ok) {
        console.log('过期积分处理完成:', result)
        await fetchData() // 刷新数据
        // 可以添加成功提示
      } else {
        console.error('处理过期积分失败:', result.error)
      }
    } catch (error) {
      console.error('Failed to process expired points:', error)
    } finally {
      setProcessingExpired(false)
    }
  }

  // 导出数据
  const exportData = async (type: 'users' | 'transactions') => {
    try {
      const response = await fetch(`/api/admin/points/export?type=${type}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `points_${type}_export_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Failed to export data:', error)
    }
  }

  // 获取积分类型徽章
  const getPointsTypeBadge = (type?: string) => {
    switch (type) {
      case 'activity':
        return <Badge variant="secondary" className="bg-green-100 text-green-800"><Activity className="w-3 h-3 mr-1" />活动</Badge>
      case 'subscription':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800"><Star className="w-3 h-3 mr-1" />订阅</Badge>
      case 'recharge':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800"><Zap className="w-3 h-3 mr-1" />充值</Badge>
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  // 获取交易类型图标
  const getTransactionIcon = (type: string, sourceType: string) => {
    if (type === 'earn') {
      switch (sourceType) {
        case 'registration':
          return <Plus className="h-4 w-4 text-green-600" />
        case 'recharge':
          return <DollarSign className="h-4 w-4 text-blue-600" />
        case 'invitation':
          return <Gift className="h-4 w-4 text-purple-600" />
        default:
          return <TrendingUp className="h-4 w-4 text-green-600" />
      }
    } else {
      return <TrendingDown className="h-4 w-4 text-red-600" />
    }
  }

  // 获取来源类型描述
  const getSourceTypeText = (sourceType: string) => {
    const sourceMap: Record<string, string> = {
      'registration': '注册奖励',
      'ai_request': 'AI对话消费',
      'export': '导出消费',
      'recharge': '充值获得',
      'subscription': '订阅获得',
      'admin_adjust': '管理员调整',
      'refund': '退款',
      'invitation': '邀请奖励'
    }
    return sourceMap[sourceType] || sourceType
  }

  if (loading) {
    return (
      <div className="flex-1 p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-64 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="admin-card p-6">
                <div className="h-4 bg-muted rounded w-20 mb-4"></div>
                <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                <div className="h-3 bg-muted rounded w-24"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold flex items-center">
          <Coins className="mr-3 h-8 w-8" />
          积分管理
        </h1>
        <p className="text-muted-foreground mt-2">
          全面管理用户积分系统，包括积分统计、交易记录和用户积分调整
        </p>
      </div>

      {/* 快速操作 */}
      <div className="flex gap-4 mb-6">
        <Button
          variant="outline"
          onClick={fetchExpiringPoints}
          className="flex items-center space-x-2"
        >
          <Calendar className="h-4 w-4" />
          <span>即将过期积分</span>
        </Button>
        <Button
          variant="outline"
          onClick={processExpiredPoints}
          disabled={processingExpired}
          className="flex items-center space-x-2"
        >
          <TrendingDown className="h-4 w-4" />
          <span>{processingExpired ? '处理中...' : '处理过期积分'}</span>
        </Button>
      </div>

      {/* 统计概览 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
                <Coins className="h-5 w-5 text-yellow-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.totalPoints)}</p>
              <p className="text-sm text-muted-foreground">总积分余额</p>
            </div>
          </div>
          
          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/20">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.totalEarned)}</p>
              <p className="text-sm text-muted-foreground">总获得积分</p>
            </div>
          </div>
          
          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-red-100 dark:bg-red-900/20">
                <TrendingDown className="h-5 w-5 text-red-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.totalSpent)}</p>
              <p className="text-sm text-muted-foreground">总消费积分</p>
            </div>
          </div>
          
          <div className="admin-stats-card">
            <div className="flex items-center justify-between mb-4">
              <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
            </div>
            <div>
              <p className="text-2xl font-bold">{formatNumber(stats.activeUsers)}</p>
              <p className="text-sm text-muted-foreground">活跃用户</p>
            </div>
          </div>
        </div>
      )}

      {/* 标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">积分概览</TabsTrigger>
          <TabsTrigger value="users">用户积分</TabsTrigger>
          <TabsTrigger value="transactions">交易记录</TabsTrigger>
        </TabsList>

        {/* 积分概览 */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 积分分布 */}
            <div className="admin-card">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold">积分类型分布</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Activity className="h-4 w-4 text-green-600" />
                      <span>活动积分</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatNumber(stats?.totalPoints * 0.4 || 0)}</div>
                      <div className="text-sm text-muted-foreground">40%</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Star className="h-4 w-4 text-blue-600" />
                      <span>订阅积分</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatNumber(stats?.totalPoints * 0.35 || 0)}</div>
                      <div className="text-sm text-muted-foreground">35%</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Zap className="h-4 w-4 text-purple-600" />
                      <span>充值积分</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatNumber(stats?.totalPoints * 0.25 || 0)}</div>
                      <div className="text-sm text-muted-foreground">25%</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 最近交易 */}
            <div className="admin-card">
              <div className="p-6 border-b border-border">
                <h3 className="text-lg font-semibold">最近交易</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {transactions.slice(0, 5).map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getTransactionIcon(transaction.transaction_type, transaction.source_type)}
                        <div>
                          <div className="font-medium">{transaction.user_name}</div>
                          <div className="text-sm text-muted-foreground">
                            {getSourceTypeText(transaction.source_type)}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-medium ${
                          transaction.transaction_type === 'earn' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.transaction_type === 'earn' ? '+' : '-'}{formatNumber(transaction.points_amount)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {formatDate(transaction.created_at)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* 用户积分 */}
        <TabsContent value="users" className="space-y-6">
          <div className="admin-card">
            <div className="p-6 border-b border-border">
              <div className="flex items-center justify-between">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="搜索用户..."
                    className="pl-10 pr-4 py-2 border border-input rounded-md bg-background text-foreground"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button variant="outline" onClick={() => exportData('users')}>
                  <Download className="h-4 w-4 mr-2" />
                  导出用户积分
                </Button>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>用户信息</th>
                    <th>当前积分</th>
                    <th>总获得</th>
                    <th>总消费</th>
                    <th>积分构成</th>
                    <th>最后交易</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {userPoints.filter(user =>
                    user.nickname?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    user.phone?.includes(searchTerm) ||
                    user.id.toString().includes(searchTerm)
                  ).map((user) => (
                    <tr key={user.id}>
                      <td>
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-yellow-500 to-orange-600 flex items-center justify-center text-white font-medium">
                            {user.nickname ? user.nickname.charAt(0).toUpperCase() : user.id.toString().slice(-1)}
                          </div>
                          <div>
                            <div className="font-medium">
                              {user.nickname || `用户${user.id}`}
                            </div>
                            <div className="text-sm text-muted-foreground font-mono">
                              ID: {user.id} | {user.phone ?
                                `${user.phone.slice(0, 3)}****${user.phone.slice(-4)}` :
                                '未绑定手机'
                              }
                            </div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className="text-lg font-bold text-yellow-600">
                          {formatNumber(user.points)}
                        </div>
                      </td>
                      <td>
                        <div className="text-green-600 font-medium">
                          +{formatNumber(user.total_earned_points)}
                        </div>
                      </td>
                      <td>
                        <div className="text-red-600 font-medium">
                          -{formatNumber(user.total_spent_points)}
                        </div>
                      </td>
                      <td>
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2 text-sm">
                            <Activity className="w-3 h-3 text-green-600" />
                            <span>{formatNumber(user.points_breakdown?.activity || 0)}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm">
                            <Star className="w-3 h-3 text-blue-600" />
                            <span>{formatNumber(user.points_breakdown?.subscription || 0)}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm">
                            <Zap className="w-3 h-3 text-purple-600" />
                            <span>{formatNumber(user.points_breakdown?.recharge || 0)}</span>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className="text-sm">
                          {user.last_transaction_at ?
                            formatDate(user.last_transaction_at) :
                            '无交易记录'
                          }
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => fetchUserDetail(user.id)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>

                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedUser(user)
                                setAdjustType('add')
                                setShowAdjustDialog(true)
                              }}
                              className="text-green-600 hover:text-green-700"
                              title="增加积分"
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedUser(user)
                                setAdjustType('subtract')
                                setShowAdjustDialog(true)
                              }}
                              className="text-red-600 hover:text-red-700"
                              title="扣减积分"
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        {/* 交易记录 */}
        <TabsContent value="transactions" className="space-y-6">
          <div className="admin-card">
            <div className="p-6 border-b border-border">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="搜索用户或交易..."
                    className="pl-10 pr-4 py-2 w-full border border-input rounded-md bg-background text-foreground"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <div className="flex gap-2">
                  <Select value={filters.transactionType} onValueChange={(value: any) => setFilters(prev => ({ ...prev, transactionType: value }))}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="交易类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="earn">获得</SelectItem>
                      <SelectItem value="spend">消费</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filters.sourceType} onValueChange={(value: any) => setFilters(prev => ({ ...prev, sourceType: value }))}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="来源" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部来源</SelectItem>
                      <SelectItem value="registration">注册奖励</SelectItem>
                      <SelectItem value="ai_request">AI对话</SelectItem>
                      <SelectItem value="recharge">充值</SelectItem>
                      <SelectItem value="subscription">订阅</SelectItem>
                      <SelectItem value="admin_adjust">管理员调整</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filters.dateRange} onValueChange={(value: any) => setFilters(prev => ({ ...prev, dateRange: value }))}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="时间" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7d">7天内</SelectItem>
                      <SelectItem value="30d">30天内</SelectItem>
                      <SelectItem value="90d">90天内</SelectItem>
                      <SelectItem value="all">全部时间</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button variant="outline" onClick={() => exportData('transactions')}>
                    <Download className="h-4 w-4 mr-2" />
                    导出
                  </Button>
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>交易ID</th>
                    <th>用户</th>
                    <th>类型</th>
                    <th>积分变动</th>
                    <th>余额变化</th>
                    <th>来源</th>
                    <th>积分类型</th>
                    <th>时间</th>
                    <th>说明</th>
                  </tr>
                </thead>
                <tbody>
                  {transactions.map((transaction) => (
                    <tr key={transaction.id}>
                      <td className="font-mono">#{transaction.id}</td>
                      <td>
                        <div className="font-medium">{transaction.user_name}</div>
                        <div className="text-sm text-muted-foreground">ID: {transaction.user_id}</div>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2">
                          {getTransactionIcon(transaction.transaction_type, transaction.source_type)}
                          <Badge variant={transaction.transaction_type === 'earn' ? 'default' : 'secondary'}>
                            {transaction.transaction_type === 'earn' ? '获得' : '消费'}
                          </Badge>
                        </div>
                      </td>
                      <td>
                        <div className={`font-bold ${
                          transaction.transaction_type === 'earn' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {transaction.transaction_type === 'earn' ? '+' : '-'}{formatNumber(transaction.points_amount)}
                        </div>
                      </td>
                      <td>
                        <div className="text-sm">
                          <div>{formatNumber(transaction.balance_before)} → {formatNumber(transaction.balance_after)}</div>
                        </div>
                      </td>
                      <td>
                        <div className="text-sm">
                          {getSourceTypeText(transaction.source_type)}
                        </div>
                      </td>
                      <td>
                        {transaction.points_type ? getPointsTypeBadge(transaction.points_type) : '-'}
                      </td>
                      <td>
                        <div className="text-sm">
                          {formatDate(transaction.created_at)}
                        </div>
                      </td>
                      <td>
                        <div className="text-sm text-muted-foreground max-w-32 truncate">
                          {transaction.description || '-'}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {transactions.length === 0 && (
              <div className="p-12 text-center text-muted-foreground">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">暂无交易记录</p>
                <p className="text-sm">
                  {searchTerm ? '没有找到匹配的交易记录' : '系统中还没有积分交易记录'}
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* 用户详情对话框 */}
      <Dialog open={showUserDetail} onOpenChange={setShowUserDetail}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Coins className="h-5 w-5" />
              <span>用户积分详情</span>
            </DialogTitle>
            <DialogDescription>
              查看用户的积分详细信息和交易历史
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">基本信息</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-r from-yellow-500 to-orange-600 flex items-center justify-center text-white text-xl font-bold">
                        {selectedUser.nickname ? selectedUser.nickname.charAt(0).toUpperCase() : selectedUser.id.toString().slice(-1)}
                      </div>
                      <div>
                        <div className="font-medium text-lg">
                          {selectedUser.nickname || `用户${selectedUser.id}`}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          ID: {selectedUser.id}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">手机号:</span>
                        <div className="font-mono">
                          {selectedUser.phone || '未绑定'}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">注册时间:</span>
                        <div>{formatDate(selectedUser.created_at)}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">积分统计</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-muted-foreground">当前积分:</span>
                      <div className="text-2xl font-bold text-yellow-600">
                        {formatNumber(selectedUser.points)}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">总获得:</span>
                        <div className="text-lg font-semibold text-green-600">
                          +{formatNumber(selectedUser.total_earned_points)}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">总消费:</span>
                        <div className="text-lg font-semibold text-red-600">
                          -{formatNumber(selectedUser.total_spent_points)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 积分构成 */}
              <div>
                <h3 className="text-lg font-semibold mb-4">积分构成</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="admin-stats-card">
                    <div className="flex items-center justify-between mb-2">
                      <Activity className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-xl font-bold">{formatNumber(selectedUser.points_breakdown?.activity || 0)}</p>
                      <p className="text-sm text-muted-foreground">活动积分</p>
                    </div>
                  </div>

                  <div className="admin-stats-card">
                    <div className="flex items-center justify-between mb-2">
                      <Star className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-xl font-bold">{formatNumber(selectedUser.points_breakdown?.subscription || 0)}</p>
                      <p className="text-sm text-muted-foreground">订阅积分</p>
                    </div>
                  </div>

                  <div className="admin-stats-card">
                    <div className="flex items-center justify-between mb-2">
                      <Zap className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-xl font-bold">{formatNumber(selectedUser.points_breakdown?.recharge || 0)}</p>
                      <p className="text-sm text-muted-foreground">充值积分</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 积分调整对话框 */}
      <Dialog open={showAdjustDialog} onOpenChange={setShowAdjustDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              {adjustType === 'add' ? <Plus className="h-5 w-5 text-green-600" /> : <Minus className="h-5 w-5 text-red-600" />}
              <span>{adjustType === 'add' ? '增加' : '扣减'}用户积分</span>
            </DialogTitle>
            <DialogDescription>
              为用户 <span className="font-medium">{selectedUser?.nickname || `用户${selectedUser?.id}`}</span> {adjustType === 'add' ? '增加' : '扣减'}积分
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* 当前积分显示 */}
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-sm text-muted-foreground">当前积分余额</div>
              <div className="text-2xl font-bold text-yellow-600">
                {formatNumber(selectedUser?.points || 0)}
              </div>
            </div>

            {/* 积分数量 */}
            <div>
              <label className="text-sm font-medium">积分数量 *</label>
              <input
                type="number"
                placeholder="请输入积分数量"
                className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                value={adjustAmount}
                onChange={(e) => setAdjustAmount(e.target.value)}
                min="1"
              />
            </div>

            {/* 积分类型（仅增加时显示） */}
            {adjustType === 'add' && (
              <div>
                <label className="text-sm font-medium">积分类型 *</label>
                <Select value={adjustPointsType} onValueChange={(value: any) => setAdjustPointsType(value)}>
                  <SelectTrigger className="w-full mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="activity">
                      <div className="flex items-center space-x-2">
                        <Activity className="h-4 w-4 text-green-600" />
                        <span>活动积分</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="subscription">
                      <div className="flex items-center space-x-2">
                        <Star className="h-4 w-4 text-blue-600" />
                        <span>订阅积分</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="recharge">
                      <div className="flex items-center space-x-2">
                        <Zap className="h-4 w-4 text-purple-600" />
                        <span>充值积分</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* 有效期（仅增加时显示） */}
            {adjustType === 'add' && (
              <div>
                <label className="text-sm font-medium">有效期 *</label>
                <Select value={adjustValidityDays} onValueChange={setAdjustValidityDays}>
                  <SelectTrigger className="w-full mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7天</SelectItem>
                    <SelectItem value="30">30天</SelectItem>
                    <SelectItem value="90">90天</SelectItem>
                    <SelectItem value="180">180天</SelectItem>
                    <SelectItem value="365">365天</SelectItem>
                    <SelectItem value="999999">永久有效</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-xs text-muted-foreground mt-1">
                  积分将在 {adjustValidityDays === '999999' ? '永久有效' : `${adjustValidityDays}天后过期`}
                </div>
              </div>
            )}

            {/* 调整原因 */}
            <div>
              <label className="text-sm font-medium">调整原因</label>
              <textarea
                placeholder="请输入调整原因（可选）"
                className="w-full mt-1 px-3 py-2 border border-input rounded-md bg-background text-foreground"
                rows={3}
                value={adjustReason}
                onChange={(e) => setAdjustReason(e.target.value)}
              />
            </div>

            {/* 预览变化 */}
            {adjustAmount && parseInt(adjustAmount) > 0 && (
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-sm font-medium text-blue-800 dark:text-blue-200">预览变化</div>
                <div className="text-sm text-blue-600 dark:text-blue-300 mt-1">
                  {adjustType === 'add' ? '增加' : '扣减'} {formatNumber(parseInt(adjustAmount))} 积分
                  {adjustType === 'add' && (
                    <span className="ml-2">
                      ({adjustPointsType === 'activity' ? '活动' : adjustPointsType === 'subscription' ? '订阅' : '充值'}积分)
                    </span>
                  )}
                </div>
                <div className="text-sm text-blue-600 dark:text-blue-300">
                  调整后余额: {formatNumber(
                    (selectedUser?.points || 0) +
                    (adjustType === 'add' ? parseInt(adjustAmount) : -parseInt(adjustAmount))
                  )}
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setShowAdjustDialog(false)
                  setAdjustAmount('')
                  setAdjustReason('')
                  setAdjustPointsType('activity')
                  setAdjustValidityDays('30')
                }}
                disabled={adjusting}
              >
                取消
              </Button>
              <Button
                onClick={adjustUserPoints}
                className={adjustType === 'add' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
                disabled={!adjustAmount || parseInt(adjustAmount) <= 0 || adjusting}
              >
                {adjusting ? '处理中...' : (adjustType === 'add' ? '增加积分' : '扣减积分')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 即将过期积分对话框 */}
      <Dialog open={showExpiringDialog} onOpenChange={setShowExpiringDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>即将过期的积分</span>
            </DialogTitle>
            <DialogDescription>
              以下积分将在7天内过期，请及时提醒用户使用
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {expiringPoints.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">用户</th>
                      <th className="text-left p-2">积分类型</th>
                      <th className="text-left p-2">积分数量</th>
                      <th className="text-left p-2">过期时间</th>
                      <th className="text-left p-2">剩余天数</th>
                    </tr>
                  </thead>
                  <tbody>
                    {expiringPoints.map((point: any) => (
                      <tr key={point.id} className="border-b">
                        <td className="p-2">
                          <div>
                            <div className="font-medium">
                              {point.nickname || `用户${point.user_id}`}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              ID: {point.user_id}
                            </div>
                          </div>
                        </td>
                        <td className="p-2">
                          {point.points_type === 'activity' && (
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                              <Activity className="w-3 h-3 mr-1" />活动
                            </Badge>
                          )}
                          {point.points_type === 'subscription' && (
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                              <Star className="w-3 h-3 mr-1" />订阅
                            </Badge>
                          )}
                          {point.points_type === 'recharge' && (
                            <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                              <Zap className="w-3 h-3 mr-1" />充值
                            </Badge>
                          )}
                        </td>
                        <td className="p-2">
                          <span className="font-medium text-yellow-600">
                            {formatNumber(point.points_amount)}
                          </span>
                        </td>
                        <td className="p-2">
                          <span className="text-sm">
                            {formatDate(point.expires_at)}
                          </span>
                        </td>
                        <td className="p-2">
                          <Badge
                            variant={point.days_until_expiry <= 1 ? "destructive" : point.days_until_expiry <= 3 ? "secondary" : "outline"}
                          >
                            {point.days_until_expiry}天
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>暂无即将过期的积分</p>
              </div>
            )}

            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setShowExpiringDialog(false)}>
                关闭
              </Button>
              {expiringPoints.length > 0 && (
                <Button
                  onClick={processExpiredPoints}
                  disabled={processingExpired}
                  className="bg-red-600 hover:bg-red-700"
                >
                  {processingExpired ? '处理中...' : '立即处理过期积分'}
                </Button>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
