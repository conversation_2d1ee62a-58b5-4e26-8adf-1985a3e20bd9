import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

export async function GET() {
  try {
    const types = await adminQueries.getExportTypes()
    return NextResponse.json(types)
  } catch (error) {
    console.error('Failed to fetch export types:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { types } = await request.json()
    
    if (!Array.isArray(types)) {
      return NextResponse.json(
        { error: 'Invalid types format' },
        { status: 400 }
      )
    }

    await adminQueries.updateExportTypes(types)
    
    return NextResponse.json({ 
      success: true, 
      message: '导出类型配置更新成功' 
    })
  } catch (error) {
    console.error('Failed to update export types:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
