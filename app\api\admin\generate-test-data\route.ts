import { NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database'

export async function POST() {
  try {
    console.log('🚀 开始生成测试数据...')
    
    // 获取所有用户
    const users = await executeQuery('SELECT id FROM users') as any[]
    console.log(`📊 找到 ${users.length} 个用户`)
    
    if (users.length === 0) {
      return NextResponse.json({ error: '没有找到用户，请先创建用户' }, { status: 400 })
    }
    
    // 生成过去30天的数据
    const today = new Date()
    const testData = []
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      
      for (const user of users) {
        // 随机生成一些用户在这一天有活动
        if (Math.random() > 0.3) { // 70% 概率有活动
          const requests = Math.floor(Math.random() * 20) + 1 // 1-20个请求
          const cost = requests * (Math.random() * 0.01 + 0.001) // 随机成本
          const membershipType = ['free', 'pro', 'max'][Math.floor(Math.random() * 3)]
          
          testData.push({
            user_id: user.id,
            date: dateStr,
            requests_count: requests,
            membership_type: membershipType,
            cost_yuan: cost
          })
        }
      }
    }
    
    console.log(`📝 生成了 ${testData.length} 条测试数据`)
    
    // 批量插入数据
    let successCount = 0
    for (const data of testData) {
      try {
        await executeQuery(`
          INSERT INTO usage_stats (user_id, date, requests_count, membership_type, cost_yuan)
          VALUES (?, ?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE
          requests_count = requests_count + VALUES(requests_count),
          cost_yuan = cost_yuan + VALUES(cost_yuan)
        `, [
          data.user_id,
          data.date,
          data.requests_count,
          data.membership_type,
          data.cost_yuan
        ])
        successCount++
      } catch (error: any) {
        console.log(`⚠️ 插入数据失败: ${error.message}`)
      }
    }
    
    console.log('✅ 测试数据生成完成！')
    
    // 验证数据
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_records,
        SUM(requests_count) as total_requests,
        SUM(cost_yuan) as total_cost,
        COUNT(DISTINCT user_id) as active_users
      FROM usage_stats
    `) as any[]
    
    console.log('📊 数据统计:', stats[0])
    
    return NextResponse.json({
      success: true,
      message: '测试数据生成成功',
      generatedCount: successCount,
      totalRecords: testData.length,
      stats: stats[0]
    })
    
  } catch (error: any) {
    console.error('❌ 生成测试数据失败:', error)
    return NextResponse.json(
      { error: '生成测试数据失败', details: error.message },
      { status: 500 }
    )
  }
} 