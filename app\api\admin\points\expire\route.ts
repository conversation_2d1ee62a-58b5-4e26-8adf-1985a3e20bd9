import { NextRequest, NextResponse } from 'next/server'
import { adminQueries } from '@/lib/database'

// 处理过期积分
export async function POST() {
  try {
    const result = await adminQueries.processExpiredPoints()
    return NextResponse.json(result)
  } catch (error) {
    console.error('Failed to process expired points:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 获取即将过期的积分
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')
    
    const expiringPoints = await adminQueries.getExpiringPoints(days)
    return NextResponse.json(expiringPoints)
  } catch (error) {
    console.error('Failed to fetch expiring points:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
