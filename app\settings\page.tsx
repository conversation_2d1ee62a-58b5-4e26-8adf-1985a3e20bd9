"use client"

import { useState } from 'react'
import { Settings, Database, Shield, Bell, Pa<PERSON>, Play } from 'lucide-react'
import { Button } from '@/components/ui/button'

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    autoBackup: true,
    emailNotifications: true,
    systemMaintenance: false,
    debugMode: false,
    cacheEnabled: true,
    maxUsers: 1000,
    maxProjects: 10000,
    tokenLimit: 50000,
  })
  
  const [isGeneratingData, setIsGeneratingData] = useState(false)
  const [isMigrating, setIsMigrating] = useState(false)

  const handleSettingChange = (key: string, value: boolean | number) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleGenerateTestData = async () => {
    setIsGeneratingData(true)
    try {
      const response = await fetch('/api/admin/generate-test-data', {
        method: 'POST',
      })
      
      const result = await response.json()
      
      if (result.success) {
        alert(`测试数据生成成功！\n生成了 ${result.generatedCount} 条记录`)
      } else {
        alert(`生成失败：${result.error}`)
      }
    } catch (error) {
      alert(`生成失败：${error}`)
    } finally {
      setIsGeneratingData(false)
    }
  }

  const handleMigrateDatabase = async () => {
    setIsMigrating(true)
    try {
      const response = await fetch('/api/admin/migrate-display-order', {
        method: 'POST',
      })
      
      const result = await response.json()
      
      if (result.success) {
        alert(`数据库迁移成功！\n${result.message}`)
      } else {
        alert(`迁移失败：${result.error}`)
      }
    } catch (error) {
      alert(`迁移失败：${error}`)
    } finally {
      setIsMigrating(false)
    }
  }

  const handleSave = () => {
    // TODO: 实现保存设置
    console.log('保存设置:', settings)
  }

  return (
    <div className="flex-1 p-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold flex items-center">
          <Settings className="mr-3 h-8 w-8" />
          系统设置
        </h1>
        <p className="text-muted-foreground mt-2">
          管理系统配置和运行参数
        </p>
      </div>

      <div className="space-y-6">
        {/* 数据库设置 */}
        <div className="admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold flex items-center">
              <Database className="mr-2 h-5 w-5" />
              数据库设置
            </h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">自动备份</label>
                <p className="text-sm text-muted-foreground">每日自动备份数据库</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.autoBackup}
                  onChange={(e) => handleSettingChange('autoBackup', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">缓存启用</label>
                <p className="text-sm text-muted-foreground">启用Redis缓存提升性能</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.cacheEnabled}
                  onChange={(e) => handleSettingChange('cacheEnabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* 安全设置 */}
        <div className="admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              安全设置
            </h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">维护模式</label>
                <p className="text-sm text-muted-foreground">启用后用户无法访问系统</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.systemMaintenance}
                  onChange={(e) => handleSettingChange('systemMaintenance', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 dark:peer-focus:ring-red-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-red-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">调试模式</label>
                <p className="text-sm text-muted-foreground">启用详细日志记录</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.debugMode}
                  onChange={(e) => handleSettingChange('debugMode', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-yellow-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* 系统限制 */}
        <div className="admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold flex items-center">
              <Palette className="mr-2 h-5 w-5" />
              系统限制
            </h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium block mb-2">最大用户数</label>
                <input
                  type="number"
                  value={settings.maxUsers}
                  onChange={(e) => handleSettingChange('maxUsers', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground"
                />
              </div>
              <div>
                <label className="text-sm font-medium block mb-2">最大项目数</label>
                <input
                  type="number"
                  value={settings.maxProjects}
                  onChange={(e) => handleSettingChange('maxProjects', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground"
                />
              </div>
              <div>
                <label className="text-sm font-medium block mb-2">Token限制</label>
                <input
                  type="number"
                  value={settings.tokenLimit}
                  onChange={(e) => handleSettingChange('tokenLimit', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 开发工具 */}
        <div className="admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold flex items-center">
              <Play className="mr-2 h-5 w-5" />
              开发工具
            </h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">数据库迁移</label>
                <p className="text-sm text-muted-foreground">为community_projects表添加display_order字段</p>
              </div>
              <Button
                onClick={handleMigrateDatabase}
                disabled={isMigrating}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isMigrating ? '迁移中...' : '执行迁移'}
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">生成测试数据</label>
                <p className="text-sm text-muted-foreground">为usage_stats表生成过去30天的测试数据</p>
              </div>
              <Button
                onClick={handleGenerateTestData}
                disabled={isGeneratingData}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {isGeneratingData ? '生成中...' : '生成测试数据'}
              </Button>
            </div>
          </div>
        </div>

        {/* 通知设置 */}
        <div className="admin-card">
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold flex items-center">
              <Bell className="mr-2 h-5 w-5" />
              通知设置
            </h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium">邮件通知</label>
                <p className="text-sm text-muted-foreground">系统异常时发送邮件通知</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.emailNotifications}
                  onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* 保存按钮 */}
        <div className="flex justify-end">
          <Button onClick={handleSave} className="px-8">
            保存设置
          </Button>
        </div>
      </div>
    </div>
  )
} 